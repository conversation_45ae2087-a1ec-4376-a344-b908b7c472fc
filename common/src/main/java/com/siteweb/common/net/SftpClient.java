package com.siteweb.common.net;

import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.Session;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * SFTP客户端，基于JSch和Hutool库实现
 * 提供文件上传、下载、目录操作等功能
 * 实现AutoCloseable接口，支持try-with-resources语法
 */
@Slf4j
@Data
public class SftpClient implements AutoCloseable {
    /** 服务器地址 */
    private String host = "localhost";
    /** SFTP 端口，默认 22 */
    private int port = 22;
    /** 用户名 */
    private String username = "root";
    /** 密码 */
    private String password = "";
    /** 远程路径，仅做记录 */
    private String remotePath = ".";
    /** 连接超时时间（毫秒） */
    private int connectTimeout = 10000;
    /** 会话超时时间（毫秒） */
    private int sessionTimeout = 30000;

    private Session session;
    private Sftp sftp;

    public SftpClient(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public SftpClient(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    /**
     * 连接并登录到SFTP服务器
     */
    public void login() throws IOException {
        try {
            // 使用Hutool的JschUtil创建Session
            session = JschUtil.createSession(host, port, username, password, null);
            session.setTimeout(sessionTimeout);
            session.connect(connectTimeout);
            
            // 创建SFTP连接
            sftp = new Sftp(session);
            
            log.info("成功连接到SFTP服务器: {}:{}", host, port);
        } catch (Exception e) {
            throw new IOException("连接SFTP服务器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 断开连接
     */
    public void logout() {
        if (sftp != null) {
            try {
                sftp.close();
                log.info("SFTP连接已关闭");
            } catch (Exception e) {
                log.error("关闭SFTP连接时出错: {}", e.getMessage());
            }
        }
        
        if (session != null && session.isConnected()) {
            try {
                session.disconnect();
                log.info("已断开与SFTP服务器的连接");
            } catch (Exception e) {
                log.error("断开SFTP服务器时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 切换远程目录
     */
    public void changeDirectory(String dirName) throws IOException {
        try {
            sftp.cd(dirName);
            this.remotePath = sftp.pwd();
            log.info("工作目录已切换到: {}", this.remotePath);
        } catch (Exception e) {
            throw new IOException("切换目录失败: " + dirName + ", 错误: " + e.getMessage(), e);
        }
    }

    /**
     * 递归下载目录
     *
     * @param remoteDirPath   远程目录
     * @param localParentDir  本地目标目录
     * @param excludePatterns 排除规则
     */
    public void downloadDirectory(String remoteDirPath, String localParentDir, List<String> excludePatterns) throws IOException {
        try {
            List<String> files = sftp.ls(remoteDirPath);
            if (Objects.isNull(files)) {
                return;
            }

            for (String fileName : files) {
                
                // 跳过当前目录和父目录
                if (".".equals(fileName) || "..".equals(fileName)) {
                    continue;
                }

                if (shouldExclude(fileName, excludePatterns)) {
                    log.info("排除文件/目录: {}", fileName);
                    continue;
                }

                String remoteFilePath = remoteDirPath + "/" + fileName;
                File localFile = new File(localParentDir, fileName);

                try {
                    // 尝试下载文件，如果是目录会抛出异常
                    sftp.download(remoteFilePath, localFile);
                    log.info("文件下载成功: {}", remoteFilePath);
                } catch (Exception e) {
                    // 如果下载失败，可能是目录，尝试递归下载
                    if (!localFile.exists() && !localFile.mkdirs()) {
                        throw new IOException("创建本地目录失败: " + localFile.getAbsolutePath());
                    }
                    try {
                        downloadDirectory(remoteFilePath, localFile.getAbsolutePath(), excludePatterns);
                    } catch (Exception dirException) {
                        log.warn("跳过无法处理的项目: {}, 错误: {}", fileName, dirException.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("下载目录时发生错误: " + e.getMessage(), e);
        }
    }

 

    /**
     * 下载文件到本地
     *
     * @param remoteFilePath 远程文件路径
     * @param localFilePath  本地文件路径
     */
    public void downloadFile(String remoteFilePath, String localFilePath) throws IOException {
        try {
            sftp.download(remoteFilePath, new File(localFilePath));
            log.info("文件下载成功: {} -> {}", remoteFilePath, localFilePath);
        } catch (Exception e) {
            throw new IOException("下载文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除远程文件
     *
     * @param remoteFilePath 远程文件路径
     */
    public void deleteFile(String remoteFilePath) throws IOException {
        try {
            sftp.delFile(remoteFilePath);
            log.info("文件删除成功: {}", remoteFilePath);
        } catch (Exception e) {
            throw new IOException("删除文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建远程目录
     *
     * @param remoteDirPath 远程目录路径
     */
    public void createDirectory(String remoteDirPath) throws IOException {
        try {
            sftp.mkdir(remoteDirPath);
            log.info("目录创建成功: {}", remoteDirPath);
        } catch (Exception e) {
            throw new IOException("创建目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除远程目录
     *
     * @param remoteDirPath 远程目录路径
     */
    public void deleteDirectory(String remoteDirPath) throws IOException {
        try {
            sftp.delDir(remoteDirPath);
            log.info("目录删除成功: {}", remoteDirPath);
        } catch (Exception e) {
            throw new IOException("删除目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param remoteFilePath 远程文件路径
     * @return 文件是否存在
     */
    public boolean fileExists(String remoteFilePath) {
        try {
            return sftp.exist(remoteFilePath);
        } catch (Exception e) {
            log.error("检查文件是否存在时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前工作目录
     *
     * @return 当前工作目录路径
     */
    public String getCurrentDirectory() throws IOException {
        try {
            return sftp.pwd();
        } catch (Exception e) {
            throw new IOException("获取当前目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否应该排除文件/目录
     */
    private boolean shouldExclude(String fileName, List<String> excludePatterns) {
        if (excludePatterns == null || excludePatterns.isEmpty()) {
            return false;
        }
        for (String pattern : excludePatterns) {
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (fileName.matches(regex)) {
                    return true;
                }
            } else if (fileName.equals(pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定目录下的文件列表
     *
     * @param remoteDirPath 远程目录路径
     * @return 文件名列表
     * @throws IOException 获取文件列表失败
     */
    public List<String> listFiles(String remoteDirPath) throws IOException {
        if (sftp == null) {
            throw new IOException("SFTP连接未建立");
        }
        try {
            return sftp.ls(remoteDirPath);
        } catch (Exception e) {
            throw new IOException("获取文件列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查连接状态
     *
     * @return 是否已连接
     */
    public boolean isConnected() {
        return session != null && session.isConnected() && sftp != null;
    }

    /**
     * 实现AutoCloseable接口，支持try-with-resources语法
     */
    @Override
    public void close() {
        logout();
    }

    /**
     * 测试方法
     * 使用方法：修改下面的连接参数，然后运行main方法
     */
    public static void main(String[] args) {
        // 测试连接参数 - 请根据实际情况修改
        String host = "*************";  // SFTP服务器地址
        int port = 22;                  // SFTP端口
        String username = "testuser";   // 用户名
        String password = "testpass";   // 密码

        System.out.println("开始测试 SftpClient...");

        // 使用 try-with-resources 确保连接自动关闭
        try (SftpClient sftpClient = new SftpClient(host, port, username, password)) {

            // 测试连接
            System.out.println("正在连接到 SFTP 服务器...");
            sftpClient.login();
            System.out.println("连接成功！");

            // 测试获取当前目录
            String currentDir = sftpClient.getCurrentDirectory();
            System.out.println("当前工作目录: " + currentDir);

            // 测试列出文件
            System.out.println("当前目录下的文件列表:");
            List<String> files = sftpClient.listFiles(".");
            if (files != null) {
                for (String file : files) {
                    System.out.println("  - " + file);
                }
            }

            // 测试检查文件是否存在
            String testFile = "test.txt";
            boolean exists = sftpClient.fileExists(testFile);
            System.out.println("文件 " + testFile + " 是否存在: " + exists);

            // 测试创建目录（可选）
            String testDir = "test_directory";
            try {
                sftpClient.createDirectory(testDir);
                System.out.println("成功创建测试目录: " + testDir);

                // 删除测试目录
                sftpClient.deleteDirectory(testDir);
                System.out.println("成功删除测试目录: " + testDir);
            } catch (IOException e) {
                System.out.println("目录操作测试失败（可能目录已存在）: " + e.getMessage());
            }

            System.out.println("所有测试完成！");

        } catch (IOException e) {
            System.err.println("SFTP 测试失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("发生未知错误: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("测试结束。");
    }
}