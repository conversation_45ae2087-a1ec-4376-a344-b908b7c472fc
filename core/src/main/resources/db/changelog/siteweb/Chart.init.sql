# 图表接口
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(25, '静态数据', '通用', '/', 'GET', '[]', 'return [["区域","A楼","B楼","C楼"],["停电站点数",600,800,50],["正常电站点数",330,1000,80]];');

INSERT INTO `chartapi`(`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES ('27', '局站/FSU/设备中断统计', '运营商多站点', '/api/interruptstatistics?resourceStructureIds={resourceStructureIds}', 'GET', '[{\"name\": \"resourceStructureIds\", \"array\": false, \"source\": \"component\", \"meaning\": \"层级(多选)\", \"children\": [], \"required\": false, \"component\": \"HierarchySelectorComponent\", \"customData\": \"\", \"defaultValue\": \"\"}]', 'const count = data && data.station && data.station.count || 0;\nconst interrupt = data && data.station && data.station.interrupt || 0;\nreturn count + \'/\' + interrupt;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(28, '用电分析', '运营商多站点', '/api/energy/total/useElectricityTrendAnalysis?startTime={startTime}&endTime={endTime}&businessTypeId=2&timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "genericList", "meaning": "时间频率", "required": true, "component": "", "customData": "[{\\"label\\":\\"每月\\",\\"value\\":\\"m\\"},{\\"label\\":\\"每日\\",\\"value\\":\\"d\\"}]", "defaultValue": ""}, {"name": "startTime,endTime", "array": false, "source": "component", "meaning": "时间长度", "required": true, "component": "TimeRangeComponent", "customData": "", "defaultValue": ""}]', 'const result = [[''alarmCount''].concat(data.xaxisData)];
data.series.map(item=>{
  let arr = [];
  arr.push(item.name);
  arr = arr.concat(item.data)
  result.push(arr)
})
return result;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(29, '多班组值班人员', '通用', '/api/shiftgroupmap/duty?shiftGroupIds={ids}', 'GET', '[{\"name\": \"ids\", \"array\": false, \"source\": \"input\", \"meaning\": \"班组id(多个逗号分隔，按顺序填入)\", \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"1,2\"}]', 'data.map((item,i)=>{\n  if(!item.employeeTitle) data[i].employeeTitle = 0;\n})\ndata.sort(function(a,b){\n  if(a.employeeTitle<b.employeeTitle) return 1;\n  if(a.employeeTitle>b.employeeTitle) return -1;\n  if(a.employeeTitle=b.employeeTitle) return 0;\n})\nconst list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式一\',\'联系方式二\'], [\'displayIndex\',\'employeeName\',\'phone\',\'mobile\'])//二参是自定义表头\nreturn list;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(30, '服务状态', '服务', 'https://10.163.100.171:8000/api/v1/software?pageIndex=1&pageSize=999', 'GET', '[]', 'const type = {
  0: ''不存在'',
  1: ''已创建'',
  2: ''运行中'',
  3: ''暂停'',
  4: ''重启中'',
  5: ''迁移中'',
  6: ''停止'',
  7: ''死亡''
}
const arr = data.data.list;
arr.map(item => item.currentState = type[item.Status]);
const list = util.formatToTable(arr, [''编号'',''服务名称'',''当前状态''], [''softwareId'',''softwareAlias'',''currentState''])
return list;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(31, '设备数量统计', '通用', '/api/equipmentcategorystatisticsstate?deviceCategoryIds=1503,1504&objectId=517000001&pageCategory=2', 'GET', '[]', '// 第三种情况,接口返回数据data格式是对象的数组,如下
const list = util.formatToTable(data, [''编号'',''数量'',], [''name'',''value'',])
return list;
//如果要对时间进行格式化
return util.arrArrFormatTime2(list, ''$M:$d'');//二参格式可选$[y|Q|M|W|d|h|m|s]
');

INSERT INTO `chartapi` VALUES(32,'权限基站汇总PUE','energy','/api/energy/energyapi/telcom/PUEOfArea?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','return 3')
,(33,'各用能类型总量','energy','/api/energy/energyapi/telcom/electCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','const arr = [data];\nconst list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn list;')
,(34,'总用电量及同环比','energy','/api/energy/energyapi/telcom/totalEnergyUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.totalElectricityValue;\n\n//return data.yoyTotalElectricityValue;\n\n//return data.qoqTotalElectricityValue;')
,(35,'碳排放及同环比','energy','/api/energy/energyapi/telcom/totalCarbonUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','return data.totalCarbonValue;\n\n')
,(36,'各用能类型碳排放总量','energy','/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.cityElectValue;\n\n//return data.oilElectValue;\n\n//return data.greenElectValue;\n')
,(37,'某基站用碳及用能总量','energy','/api/energy/energyapi/telcom/oneRoomEnergyAndCarbon?resourceStructureId={resourceStructureId}','GET','[{\"name\": \"resourceStructureId\", \"array\": false, \"source\": \"input\", \"meaning\": \"节点ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.energyTotal;\n//\n\n//return data.carbonTotal;')
,(38,'用电分项占比','energy','/api/energy/energyapi/telcom/electCategoryProportion?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\nreturn list;\n')
,(39,'下级用能碳list接口','energy','/api/energy/energyapi/telcom/nextLevelCarbonRank?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn list;\n')
,(40,'各用能类型碳排放年趋势','energy','/api/energy/energyapi/telcom/carbonCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'时间\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'time\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(41,'权限基站汇总PUE趨勢','energy','/api/energy/energyapi/telcom/PUEOfAreaTrend?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(42,'各用能类型总量年趋势','energy','/api/energy/energyapi/telcom/energyCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n//const list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\n//如果要对时间进行格式化\n//return util.arrArrFormatTime2(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n//堆叠图\n//const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\n//return list;\n\n//趋势图\n//const list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\n//return util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n\nconst list = util.formatToTable(data, [\'时间\',\'市电\',\'油机发电\',\'绿电\'], [\'time\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(43,'各用能类型碳排放总量','energy','/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','const arr = [data];\nconst list = util.formatToTable(arr, [\'市电碳排放\',\'油机碳排放\',\'绿电碳抵消\'], [\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn list;\n')
,(44,'下级用能排名','energy','/api/energy/energyapi/telcom/nextLevelEnergyRank?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','const list = util.formatToTable(data, [\'层级\',\'市电\',\'油机发电\',\'绿电\'], [\'resourceStructureName\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn list;\n')
,(45,'碳抵消同环比','energy','/api/energy/energyapi/telcom/greenCarbonUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.greenElectCarbonValue;\n\n')
,(46,'各用能类型碳排放年趋势(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/carbonCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"input\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"-1\"}]','const list = util.formatToTable(data, [\'时间\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'],   [\'time\',\'totalValue1\',\'totalValue2\',\'totalValue3\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');\n//return util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(47,'下级各用能类型总量排名(结构堆叠)(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/nextLevelEnergyRank?userId={userId}&timeType={timeType}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','const list = util.formatToTable(data, [\'名称\',\'市电\',\'油机发电\',\'绿电\'],   [\'resourceStructureName\',\'value1\',\'value2\',\'value3\'])//二参是自定义表头\nreturn list;//二参格式可选$[y|Q|M|W|d|h|m|s]')
,(48,'总用电量及同环比(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/totalEnergyUseOfRole?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\nreturn data;\n')
,(49,'碳排放及同环比(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/totalCarbonUseOfRole?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n\n')
,(50,'各用能类型总量(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/electCategoryOfRole?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\nreturn list;')
,(51,'碳抵消同环比(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/greenCarbonUseOfRole?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n')
,(52,'各用能类型碳排放总量(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/carbonCategoryOfRole?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\nreturn list;')
,(53,'某基站用碳及用能总量(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/carbonCategoryTrend?resourceStructureId={resourceStructureId}','GET','[{\"name\": \"resourceStructureId\", \"array\": false, \"source\": \"input\", \"meaning\": \"层级ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n\n')
,(54,'各用能类型总量年趋势(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/energyCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n\n')
,(55,'下级各用能类型碳排放排名(结构堆叠)(动态获取)','能耗修改','/api/energy/energyapi/telcom/dynamic/nextLevelCarbonRank?userId={userId}&timeType={timeType}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"用户Id\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n');

-- 字节告警总览图表接口 把id占用防止id冲突
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (65,'设备总数','字节定制告警总览','/api/equipmentsum','GET','[]','return data;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (66,'近N天告警设备数量','字节定制告警总览','api/alarmdevicecountbydays?days={days}','GET','[{\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','return data.value;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (67,'近N天设备告警率','字节定制告警总览','api/devicealarmratebydays?days={days}','GET','[{\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','return data.value;\n\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (68,'近N天正常设备数','字节定制告警总览','api/normaldevicecountbydays?days={days}','GET','[{\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','return data;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (56,'近N天告警数量','字节定制告警总览','api/alarmcountbydaysandtype?days={days}&unconfirmFlag={unconfirmFlag}','GET','[{\"name\": \"unconfirmFlag\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"是否是查询未确认告警\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"仅未确认\\\",\\\"value\\\":true},{\\\"label\\\":\\\"所有\\\",\\\"value\\\":false}]\", \"defaultValue\": \"false\"}, {\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','return data.value;');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (57,'设备类别告警事件统计','字节定制告警总览','api/equipmentcategoryalarmstatistics?type={type}','GET','[{\"name\": \"type\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"统计粒度\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"近7天\\\",\\\"value\\\":\\\"1\\\"},{\\\"label\\\":\\\"近一月\\\",\\\"value\\\":\\\"2\\\"},{\\\"label\\\":\\\"近一年\\\",\\\"value\\\":\\\"3\\\"},{\\\"label\\\":\\\"所有时间\\\",\\\"value\\\":\\\"4\\\"}]\", \"defaultValue\": \"1\"}]','const list = util.formatToTable(data, [\'编号\',\'数量\',], [\'id\',\'value\',])\nreturn list;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (58,'设备告警排名 TopN','字节定制告警总览','api/equipmentalarmrankstatistics?number={number}&type={type}','GET','[{\"name\": \"type\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"统计粒度\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"近7天\\\",\\\"value\\\":\\\"1\\\"},{\\\"label\\\":\\\"近一月\\\",\\\"value\\\":\\\"2\\\"},{\\\"label\\\":\\\"近一年\\\",\\\"value\\\":\\\"3\\\"},{\\\"label\\\":\\\"所有时间\\\",\\\"value\\\":\\\"4\\\"}]\", \"defaultValue\": \"1\"}, {\"name\": \"number\", \"array\": false, \"source\": \"input\", \"meaning\": \"TopN\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"10\"}]','const list = util.formatToTable(data, [\'设备\',\'数量\',], [\'name\',\'value\',])\nreturn list;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (59,'设备告警等级占比','字节定制告警总览','api/equipmentalarmlevelstatistics?type={type}','GET','[{\"name\": \"type\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"统计粒度\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"近7天\\\",\\\"value\\\":\\\"1\\\"},{\\\"label\\\":\\\"近一月\\\",\\\"value\\\":\\\"2\\\"},{\\\"label\\\":\\\"近一年\\\",\\\"value\\\":\\\"3\\\"},{\\\"label\\\":\\\"所有时间\\\",\\\"value\\\":\\\"4\\\"}]\", \"defaultValue\": \"2\"}]','const list = util.formatToChartList(data, [\'告警等级\',\'告警数量\'],[\'name\',\'value\']);\nreturn list;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (60,'区域告警排名 TopN','字节定制告警总览','api/areaalarmrankstatistics?type={type}&number={number}','GET','[{\"name\": \"type\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"统计粒度\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"近7天\\\",\\\"value\\\":\\\"1\\\"},{\\\"label\\\":\\\"近一月\\\",\\\"value\\\":\\\"2\\\"},{\\\"label\\\":\\\"近一年\\\",\\\"value\\\":\\\"3\\\"},{\\\"label\\\":\\\"所有时间\\\",\\\"value\\\":\\\"4\\\"}]\", \"defaultValue\": \"2\"}, {\"name\": \"number\", \"array\": false, \"source\": \"input\", \"meaning\": \"TopN\", \"children\": [], \"required\": false, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"10\"}]','const list = util.formatToTable(data, [\'区域\',\'告警数量\',], [\'name\',\'value\',])\nreturn list;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (61,'告警趋势','字节定制告警总览','api/alarmtrendmonthorweek?periodType={periodType}','GET','[{\"name\": \"periodType\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"统计类型（当前周、当前月）\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"当前周\\\",\\\"value\\\":\\\"1\\\"},{\\\"label\\\":\\\"当前月\\\",\\\"value\\\":\\\"2\\\"}]\", \"defaultValue\": \"1\"}]','const list = util.formatToTable(data, [\'时间\',\'一级告警\',\'二级告警\',\'三级告警\',\'四级告警\'], [\'time\',\'level1Count\',\'level2Count\',\'level3Count\',\'level4Count\']);\nreturn list;');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (62,'活动告警告警时长分析','字节定制告警总览','api/alarmdurationstatistics?objectId={objectId}&eventEnded=false','GET','[{\"name\": \"objectId\", \"array\": false, \"source\": \"component\", \"meaning\": \"告警时长统计层级\", \"children\": [], \"required\": true, \"component\": \"HierarchySelectorComponent\", \"customData\": \"\", \"defaultValue\": \"\"}]','const list = util.formatToTable(data, [\'设备名称\',\'小于10分钟\',\'10-30分钟\',\'30-60分钟\',\'60分钟以上\'], [\'baseEquipmentName\',\'count1\',\'count2\',\'count3\',\'count4\'])//二参是自定义表头\nreturn list;');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (63,'近N天告警设备列表','字节定制告警总览','api/alarmdevicelistbydays?days={days}','GET','[{\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','const list = util.formatToTable(\n  data,\n  [\n    \'告警ID\',\n    \'设备ID\',\n    \'设备基类ID\',\n    \'局站ID\',\n    \'局站名\',\n    \'设备名\',\n    \'事件ID\',\n    \'事件名\',\n    \'告警等级ID\',\n    \'告警等级名\',\n    \'开始时间\',\n    \'结束时间\',\n    \'层级ID\',\n    \'告警级别\',\n  ], // 自定义表头\n  [\n    \'sequenceId\',\n    \'equipmentId\',\n    \'baseEquipmentId\',\n    \'stationId\',\n    \'stationName\',\n    \'equipmentName\',\n    \'eventId\',\n    \'eventName\',\n    \'eventSeverityId\',\n    \'eventSeverity\',\n    \'startTime\',\n    \'endTime\',\n    \'resourceStructureId\',\n    \'eventLevel\',\n  ] // 字段名\n);\n\nreturn list;\n');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (64,'近N天告警列表','字节定制告警总览','api/alarmlistbydaysandtype?days={days}&unconfirmFlag={unconfirmFlag}','GET','[{\"name\": \"unconfirmFlag\", \"array\": false, \"source\": \"genericList\", \"meaning\": \"未确认\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"[{\\\"label\\\":\\\"仅未确认\\\",\\\"value\\\":true},{\\\"label\\\":\\\"所有\\\",\\\"value\\\":false}]\", \"defaultValue\": \"false\"}, {\"name\": \"days\", \"array\": false, \"source\": \"input\", \"meaning\": \"统计的天数\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"7\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data;\n\n// 第二种情况,接口返回数据data格式是是对象,如下\n// 一级告警: [{statisticsTime: \"2022-01-10 00:00:00\", alarmCount: 0, eventSeverityId: null, severityName: \"一级告警\"},…]\n// 三级告警: [{statisticsTime: \"2022-01-10 00:00:00\", alarmCount: 0, eventSeverityId: null, severityName: \"三级告警\"},…]\n// 二级告警: [{statisticsTime: \"2022-01-10 00:00:00\", alarmCount: 0, eventSeverityId: null, severityName: \"二级告警\"},…]\n// 四级告警: [{statisticsTime: \"2022-01-10 00:00:00\", alarmCount: 0, eventSeverityId: null, severityName: \"四级告警\"},…]     \nconst list = util.formatToChartList(data, \'alarmCount\', \'statisticsTime\', false);//第四个布尔参数表示是否要多第三个参数进行排序,可不填\nreturn list;\n//如果要对时间进行格式化\nreturn util.arrArrFormatTime(list, \'$M:$d\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\nreturn list;\n//如果要对时间进行格式化\nreturn util.arrArrFormatTime2(list, \'$M:$d\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n// 第四种情况,雷达图,接口返回数据data格式如下\n// [{equipmentCategory: 1004, equipmentCategoryName: \"环境设备\", equipmentCount: 2, onlineCount: 0, max: 10},{...}];\nconst list = util.formatRadarChartList(data, [\'设备总数\', \'在线数量\'], \'equipmentCategoryName\', [\'equipmentCount\',\'onlineCount\'],\'max\');//二参是图例名,是根据四参自己取名的,比如=[\'设备总数\',\'在线数量\']；五参最大值名称,可不填\nreturn list;\n\n// 第五种情况,仪表盘,接口返回数据data格式为number\nconst list = util.formatOneGaugeChartList(data, \'PUE\', \'#7fb5ff\');//三参是指针颜色,可不填\nreturn list;\n\n// 第六种情况,仪表盘,接口返回数据data格式如下\n// [{id: 1, name: \"vip基站\", value: 2},\n // {id: 2, name: \"普通基站\", value: 10},\n// {id: 3, name: \"重要基站\", value: 46},\n // {id: 4, name: \"边远基站\", value: 56}]\nconst list = util.formatMulGaugeChartList(data, \'name\', \'value\', [\'#7fb5ff\', \'#7fb5ff\']);//四参是指针颜色数组,可不填\nreturn list;');
-- INSERT INTO `chartapi` (`ApiId`,`ApiName`,`Category`,`Url`,`Method`,`ParamSchema`,`Transform`) VALUES (69, '告警等级排名', '字节定制', '/api/activehistoryeventseverityrankstatistics', 'GET', '[{\"name\": \"startTime,endTime\", \"array\": false, \"source\": \"component\", \"meaning\": \"时间\", \"children\": [], \"required\": true, \"component\": \"TimeRangeComponent\", \"customData\": \"\", \"defaultValue\": \"\"}]', '// \"data\": [\n//   {\n//       \"eventSeverity\": \"一级告警\",\n//       \"eventLevel\": 1,\n//       \"count\": 22,\n//       \"percent\": \"61.1%\"\n//   },\n//   {\n//       \"eventSeverity\": \"二级告警\",\n//       \"eventLevel\": 2,\n//       \"count\": 4,\n//       \"percent\": \"11.1%\"\n//   },\n//   {\n//       \"eventSeverity\": \"三级告警\",\n//       \"eventLevel\": 3,\n//       \"count\": 5,\n//       \"percent\": \"13.9%\"\n//   },\n//   {\n//       \"eventSeverity\": \"四级告警\",\n//       \"eventLevel\": 4,\n//       \"count\": 5,\n//       \"percent\": \"13.9%\"\n//   }\n// ]\nlet sum = 0;\ndata.sort((a, b) => b.count - a.count);\ndata.map(i=>{sum += i.count;});\ndata.push( {\n      \'eventSeverity\': \'合计\',\n      \'eventLevel\': 0,\n      \'count\': sum,\n      \'percent\': \'100%\'\n  });\nconst list = util.formatToTable(data, [\'告警等级\',\'告警数量\',\'占比\'], [\'eventSeverity\',\'count\',\'percent\'])//二参是自定义表头\nreturn list;');
# 图表样式
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(1, '基础柱状图', 1, 'barchart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x轴轴线颜色
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x轴文字
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y轴文字
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//分割线
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//图例
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
  series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(2, '横向柱状图', 1, 'horizontalbarchart.png', 'option = {
	grid: {
		bottom: 80
	},
	yAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x轴轴线颜色
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x轴文字
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	xAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y轴文字
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//分割线
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//图例
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(3, '堆叠柱状图', 1, 'stackbarchart.png', 'option = {
    grid: {
        bottom: 80
    },
    xAxis: {
        type: ''category'',
        data: null,
        show: true,
        axisTick: { show: false },
        boundaryGap: true,
        silent: true,
        axisLine: {//轴线
            show: true,
            lineStyle: {
                color: ''#BFDFFF'', //x轴轴线颜色
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
        }
    },
    yAxis: {
        type: ''value'',
        show: true,
        splitNumber: 4,
        max: null,
        min: 0,
        silent: true,
        textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
        axisLine: {//轴线
            show: true,
            lineStyle: {
                color: ''#BFDFFF'',
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
        },
        splitLine: {//分割线
            lineStyle: {
                width: 1,
                color: ''#BFDFFF'',
                opacity: 0.1
            }
        }
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''axis'',
        position: '''',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40,
    },
    series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(4, '折柱图', 1, 'linebarchart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x轴轴线颜色
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x轴文字
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y轴文字
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//分割线
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//图例
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(5, '堆叠折柱图', 1, 'stackbarlinechart.png', 'option = {
    grid: {
        bottom: 80
    },
    xAxis: {
        type: ''category'',
        data: null,
        show: true,
        axisTick: { show: false },
        boundaryGap: true,
        silent: true,
        axisLine: {//轴线
            show: true,
            lineStyle: {
                color: ''#BFDFFF'', //x轴轴线颜色
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
        }
    },
    yAxis: {
        type: ''value'',
        show: true,
        splitNumber: 4,
        max: null,
        min: 0,
        silent: true,
        textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
        axisLine: {//轴线
            show: true,
            lineStyle: {
                color: ''#BFDFFF'',
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
        },
        splitLine: {//分割线
            lineStyle: {
                width: 1,
                color: ''#BFDFFF'',
                opacity: 0.1
            }
        }
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''axis'',
        position: '''',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40,
    },
    series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(6, '折柱图双y轴', 1, 'linebarchartdoubleyaxis.png', 'option = {
  grid: {
    bottom: 80
  },
  xAxis: {
    type: ''category'',
    data: null,
    show: true,
    axisTick: { show: false },
    boundaryGap: true,
    silent: true,
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'', //x轴轴线颜色
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//x轴文字
      show: true,
      overflow: ''break'',
      width: null,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13,
      ellipsis: ''...''
    }
  },
  yAxis: [{
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 1000,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y轴文字
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//分割线
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }, {
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 100,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y轴文字
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//分割线
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }],
	dataZoom: [],
  dataset: {
    source: [
    ]
  },
  tooltip: {
    show: true,
    trigger: ''item'',
    position: ''top'',
    textStyle: {
      color: ''#bfdfff'',
      fontSize: 12
    },
    backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
    borderWidth: 1,
    borderColor: ''rgba(0,127,255,0.3)'',
    padding: null,
    axisPointer: {
      type: "none"
    },
  },
  legend: {//图例
    type: "scroll",
    pageIconColor: ''#aaa'',
    pageIconInactiveColor: ''#2f4554'',
    pageTextStyle: {
      color: "#aaa"
    },
    show: true,
    itemHeight: 5,
    bottom: 15,
    itemGap: 40,
  },
  series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(7, '堆叠折柱图双y轴', 1, 'stackbarlinechartdoubleyaxis.png', 'option = {
  grid: {
    bottom: 80
  },
  xAxis: {
    type: ''category'',
    data: null,
    show: true,
    axisTick: { show: false },
    boundaryGap: true,
    silent: true,
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'', //x轴轴线颜色
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//x轴文字
      show: true,
      overflow: ''break'',
      width: null,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13,
      ellipsis: ''...''
    }
  },
  yAxis: [{
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 1000,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y轴文字
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//分割线
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }, {
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 100,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//轴线
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y轴文字
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//分割线
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }],
  dataZoom: [],
  dataset: {
    source: [
    ]
  },
  tooltip: {
    show: true,
    trigger: ''axis'',
    position: '''',
    textStyle: {
      color: ''#bfdfff'',
      fontSize: 12
    },
    backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
    borderWidth: 1,
    borderColor: ''rgba(0,127,255,0.3)'',
    padding: null,
    axisPointer: {
      type: "none"
    },
  },
  legend: {//图例
    type: "scroll",
    pageIconColor: ''#aaa'',
    pageIconInactiveColor: ''#2f4554'',
    pageTextStyle: {
      color: "#aaa"
    },
    show: true,
    itemHeight: 5,
    bottom: 15,
    itemGap: 40,
  },
  series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(8, '普通饼图', 2, 'piechart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(9, '南丁格尔玫瑰饼图', 2, 'rosepiechart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(10, '圆环图', 2, 'ringchart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(11, '南丁格尔玫瑰圆环图', 2, 'roseringchart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//图例
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(12, '普通折线图', 0, 'linechart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x轴轴线颜色
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x轴文字
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y轴文字
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//分割线
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//图例
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(13, '面积图', 0, 'arealinechart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x轴轴线颜色
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x轴文字
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y轴文字
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//分割线
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//图例
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(14, '普通仪表盘', 5, 'gaugechart.png', 'option = {
  series: [
    {
      name: ''Pressure'',
      type: ''gauge'',
      radius: ''100%'',
      center: [''50%'', ''60%''],
      startAngle: 180,
      endAngle: 0,
      splitNumber: 5,
      min: 1,
      max: 200,
      data:[],
      pointer: {
        length: ''70%'',
        width: 4,
        offsetCenter: [0, 0]
      },
      progress: {
        show: false,
        overlap: false,
        roundCap: false,
        clip: false,
        itemStyle: {
          borderWidth: 1,
          borderColor: ''#464646''
        }
      },
      axisTick: {
        show: true,
        length: 10,
        splitNumber: 5,
        distance: 0,
        lineStyle: {
          width: 1,
          color: ''#ffffff''
        }
      },
      axisLabel: {
        show: true,
        distance: 50,
        textStyle: {
          fontSize: 14,
          color: ''#3895ca''
        }
      },
      splitLine: {
        show: true,
        length: 25,
        distance: -27.5,
        lineStyle: {
          width: 1,
          color: ''#ffffff'',
          opacity: 1
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          width: 30,
          color: [
            [
              1,
              {
                colorStops: [//表盘最外圈颜色和百分比
                  {
                    offset: 0,
                    color: ''#20c374''
                  },
                  {
                    offset: 0.5,
                    color: ''#FFFF00''
                  },
                  {
                    offset: 1,
                    color: ''#d15233''
                  }
                ],
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                type: ''linear'',
                global: false
              }
            ]
          ]
        }
      },
      anchor: {
        showAbove: true,
        size: 9.5,
        itemStyle: {
          borderColor: ''rgba(251, 251, 251, 1)'',
          color: ''rgba(0, 0, 0, 1)'',
          borderWidth: 2.5
        }
      },
      title: {
        show: true,
        textStyle: {
          fontSize: 30,
          color: ''#FFFFFF''
        }
      },
      detail: {
        show: true,
        formatter: ''{value}'',
        fontSize: 30,
        color: ''#FFD237''
      }
    }
  ]
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(15, '基础雷达图', 4, 'radarchart.png', 'option = {
  legend: {
    right: ''0%'',
    textStyle: {
      color: ''#BFDFFF''
    },
    padding: [0, 0, 100, 0],
    data: []
  },
  tooltip: {
    show: true
  },
  radar: {
    indicator: [],
    axisName: {
      color: ''#BFDFFF'',
      fontSize: 12
    },
    axisLine: {
      lineStyle: {
        color: ''#BFDFFF'',
        fontSize: 1,
        opacity: 0.5
      }
    },
    splitLine: {
      lineStyle: {
        color: ''#BFDFFF'',
        fontSize: 2
      }
    }
  },
  series: [
    {
      type: ''radar'',
      data: []
    },
  ]
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(16, '普通表格', 6, 'table.png', '');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(17, '文本数字', 3, 'textnumber.png', '');

# 图表主题
INSERT INTO charttheme (ThemeId, themeName, themeCode, themeData, themeDefault) VALUES(1, '默认', 'default', '{"bar": {"itemStyle": {"barBorderColor": "#ccc", "barBorderWidth": 0}}, "geo": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "map": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "pie": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "line": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "gauge": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "graph": {"color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "label": {"color": "#eee"}, "smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderColor": "#ccc", "borderWidth": 0}, "lineStyle": {"color": "#aaa", "width": 1}, "symbolSize": 4}, "radar": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "title": {"textStyle": {"color": "#464646"}, "subtextStyle": {"color": "#6E7079"}}, "funnel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "legend": {"textStyle": {"color": "#333"}}, "sankey": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "boxplot": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "logAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "scatter": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "toolbox": {"emphasis": {"iconStyle": {"borderColor": "#666"}}, "iconStyle": {"borderColor": "#999"}}, "tooltip": {"axisPointer": {"lineStyle": {"color": "#ccc", "width": 1}, "crossStyle": {"color": "#ccc", "width": 1}}}, "dataZoom": {"textStyle": {}, "handleSize": "undefined%"}, "parallel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "timeAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "timeline": {"label": {"color": "#A4B1D7"}, "emphasis": {"label": {"color": "#A4B1D7"}, "itemStyle": {"color": "#FFF"}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}}, "itemStyle": {"color": "#A4B1D7", "borderWidth": 1}, "lineStyle": {"color": "#DAE1F5", "width": 2}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}, "checkpointStyle": {"color": "#316bf3", "borderColor": "fff"}}, "markPoint": {"label": {"color": "#eee"}, "emphasis": {"label": {"color": "#eee"}}}, "textStyle": {}, "valueAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "visualMap": {"color": ["#bf444c", "#d88273", "#f6efa6"]}, "candlestick": {"itemStyle": {"color": "#eb5454", "color0": "#47b262", "borderColor": "#eb5454", "borderWidth": 1, "borderColor0": "#47b262"}}, "categoryAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "backgroundColor": "rgba(0, 0, 0, 0)"}', 1);
