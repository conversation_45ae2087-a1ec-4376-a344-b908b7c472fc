<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_chartapi_info" author="Liu.Yandong.Hanks">
        <createTable tableName="ChartApi" remarks="ChartApi info table">
            <column name="ApiId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ApiName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="Category" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="Url" type="varchar(1024)">
                <constraints nullable="false"/>
            </column>
            <column name="Method" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="ParamSchema" type="json">
                <constraints nullable="false"/>
            </column>
            <column name="Transform" type="varchar(4096)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ChartApi" columnName="ApiId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>