

CREATE TABLE `robot_alarm_comment` (
  `comment_id` int NOT NULL AUTO_INCREMENT,
  `alarm_id` varchar(36) NOT NULL,
  `comment_content` varchar(255) NOT NULL,
  `comment_time` varchar(64) DEFAULT NULL,
  `is_confirm_comment` tinyint NOT NULL,
  PRIMARY KEY (`comment_id`,`alarm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 ;

CREATE TABLE `robot_alarm` (
  `alarm_id` varchar(36) NOT NULL,
  `device_id` int DEFAULT NULL,
  `robot_id` int DEFAULT NULL,
  `alarm_name` varchar(255) DEFAULT NULL COMMENT '指示灯异常；电压过高...',
  `alarm_time` varchar(64) DEFAULT NULL COMMENT '告警时间',
  `alarm_level` int DEFAULT NULL COMMENT '1234级告警',
  `alarm_type` int DEFAULT NULL COMMENT '1-设备（巡检项）告警\\n2-环境（机器人本体）告警',
  `alarm_value` varchar(255) DEFAULT NULL COMMENT '告警值',
  `alarm_info` varchar(255) DEFAULT NULL COMMENT '巡检项名称（or环境名称）+ alarm_name',
  `dock_id` int DEFAULT NULL,
  `is_confirmed` tinyint DEFAULT NULL,
  PRIMARY KEY (`alarm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='机器人告警';

CREATE TABLE `robot_cronjob` (
  `cron_id` int NOT NULL,
  `cron_spec` varchar(255) DEFAULT NULL,
  `task_path_id` varchar(255) DEFAULT NULL,
  `task_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`cron_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;

CREATE TABLE `robot_device_type` (
  `device_type_id` int NOT NULL AUTO_INCREMENT,
  `device_type_name_chs` varchar(45) DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `alarm_level` int DEFAULT NULL,
  `threshold_value_start` varchar(255) DEFAULT NULL,
  `threshold_expression_start` varchar(45) DEFAULT NULL,
  `is_statistable` tinyint DEFAULT NULL,
  PRIMARY KEY (`device_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 ;


CREATE TABLE `robot_device` (
  `device_name` varchar(255) DEFAULT NULL,
  `device_id` varchar(255) NOT NULL,
  `dock_id` varchar(255) DEFAULT NULL,
  `robot_id` int DEFAULT NULL,
  PRIMARY KEY (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='我们理解的一个设备的巡检项';


CREATE TABLE `robot_dock_equip` (
  `dock_id` int NOT NULL,
  `equip_name` varchar(255) DEFAULT NULL,
  `assets_no` varchar(255) DEFAULT NULL,
  `room_id` int DEFAULT NULL,
  `room_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`dock_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;


CREATE TABLE `robot_dock` (
  `dock_x` varchar(255) DEFAULT NULL,
  `dock_y` varchar(255) DEFAULT NULL,
  `dock_id` varchar(255) NOT NULL,
  `robot_id` int DEFAULT NULL,
  PRIMARY KEY (`dock_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='我们理解的一台设备';

CREATE TABLE `robot_initiative_result` (
  `iar_id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(36) DEFAULT NULL,
  `robot_id` int DEFAULT NULL,
  `device_name` varchar(255) DEFAULT NULL,
  `device_result` varchar(255) DEFAULT NULL,
  `is_alarm` tinyint DEFAULT NULL,
  `pic_base64` blob,
  `alarm_level` int DEFAULT NULL,
  `device_type_name` varchar(45) DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `iar_time` datetime DEFAULT NULL,
  PRIMARY KEY (`iar_id`),
  UNIQUE KEY `idnew_table_UNIQUE` (`iar_id`)
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 ;

CREATE TABLE `robot_reports` (
  `id` int NOT NULL,
  `task_uuid` int DEFAULT NULL,
  `robot_uuid` int DEFAULT NULL,
  `taks_desc` varchar(255) DEFAULT NULL,
  `start_time` date DEFAULT NULL,
  `end_time` date DEFAULT NULL,
  `task_finish_status` int DEFAULT NULL COMMENT '任务完成状态：未开始、正在执行、完成、中断',
  `alarm` json DEFAULT NULL COMMENT '机器人报警信息',
  `task_situation` json DEFAULT NULL COMMENT '机器人巡检结果',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='任务报表信息';


CREATE TABLE `robot_robot` (
  `robot_id` int NOT NULL,
  `robot_ip` varchar(45) DEFAULT NULL,
  `robot_name` varchar(255) DEFAULT NULL,
  `robot_status` int DEFAULT NULL,
  `robot_status_name` varchar(45) DEFAULT NULL,
  `running_task_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`robot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='机器人信息';

CREATE TABLE `robot_task_template` (
  `task_path_id` varchar(255) NOT NULL,
  `robot_id` int DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `task_devices` varchar(1024) DEFAULT NULL COMMENT '模板下的巡检项列表',
  `task_estimated_time` int DEFAULT NULL,
  PRIMARY KEY (`task_path_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='任务模板';

CREATE TABLE `robot_task` (
  `task_id` varchar(255) NOT NULL,
  `task_path_id` varchar(255) DEFAULT NULL,
  `task_estimated_time` int DEFAULT NULL,
  `task_status` int DEFAULT NULL,
  `task_status_name` varchar(45) DEFAULT NULL,
  `task_start_predict_time` varchar(255) DEFAULT NULL,
  `task_start_time` varchar(255) DEFAULT NULL,
  `task_end_predict_time` varchar(255) DEFAULT NULL,
  `task_end_time` varchar(255) DEFAULT NULL,
  `task_running_time` double DEFAULT NULL,
  `task_do_year` int DEFAULT NULL,
  `robot_id` int DEFAULT NULL,
  `task_type` varchar(255) DEFAULT NULL,
  `task_template_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='任务表（由任务模板生成）';

CREATE TABLE `robot_visitor` (
  `visitor_id` varchar(36) NOT NULL,
  `robot_id` int DEFAULT NULL,
  `visitor_name` varchar(255) DEFAULT NULL,
  `visitor_sex` int DEFAULT NULL COMMENT '0女 1男',
  `visitor_company` varchar(255) DEFAULT NULL,
  `visitor_phone` varchar(255) DEFAULT NULL,
  `visitor_valid_period` int DEFAULT NULL,
  `visitor_insert_time` varchar(255) DEFAULT NULL,
  `last_visit_time` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`visitor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='访客信息表';











