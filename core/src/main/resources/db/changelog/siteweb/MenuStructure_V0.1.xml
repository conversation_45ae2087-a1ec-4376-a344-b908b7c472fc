<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_MenuStructure_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MenuStructure"
                     remarks="MenuStructure info table">
            <column name="MenuStructureId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="MenuProfileId" type="int" remarks="菜单方案ID">
                <constraints nullable="false"/>
            </column>
            <column name="ParentId" type="int" remarks="父菜单目录ID">
                <constraints nullable="true"/>
            </column>
            <column name="Title" type="varchar(255)" remarks="菜单目录名称">
                <constraints nullable="true"/>
            </column>
            <column name="Icon" type="varchar(255)" remarks="菜单目录图标">
                <constraints nullable="true"/>
            </column>
            <column name="Selected" type="tinyint(1)" remarks="是否默认选中">
                <constraints nullable="true"/>
            </column>
            <column name="Expanded" type="tinyint(1)" remarks="是否可展开">
                <constraints nullable="true"/>
            </column>
            <column name="Hidden" type="tinyint(1)" remarks="是否隐藏显示">
                <constraints nullable="true"/>
            </column>
            <column name="SortIndex" type="int" remarks="排序索引">
                <constraints nullable="true"/>
            </column>
            <column name="IsSystem" type="tinyint(1)" remarks="是否系统内置">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
            <column name="Alias" type="varchar(255)" remarks="别名">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="MenuStructure" columnName="MenuStructureId" incrementBy="1" columnDataType="int" startWith="1000"/>
    </changeSet>
</databaseChangeLog>