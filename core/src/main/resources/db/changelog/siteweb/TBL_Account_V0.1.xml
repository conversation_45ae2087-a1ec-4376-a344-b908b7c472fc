<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblAccount_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Account" remarks="tblAccount info table">
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserName" type="varchar(128)" remarks="用户名">
                <constraints nullable="false"/>
            </column>
            <column name="LogonId" type="varchar(128)" remarks="登录名">
                <constraints nullable="false"/>
            </column>
            <column name="Password" type="varchar(128)" remarks="密码">
                <constraints nullable="true"/>
            </column>
            <column defaultValue="1" name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="MaxError" type="int" remarks="最大重试次数">
                <constraints nullable="true"/>
            </column>
            <column defaultValue="0" name="Locked" type="tinyint(1)" remarks="是否被锁定">
                <constraints nullable="false"/>
            </column>
            <column name="ValidTime" type="datetime" remarks="可用时间">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column defaultValue="0" name="IsRemote" type="tinyint(1)" remarks="是否为远程">
                <constraints nullable="false"/>
            </column>
            <column name="CenterId" type="int" remarks="所属监控中心">
                <constraints nullable="true"/>
            </column>
            <column name="PasswordValidTime" type="datetime" remarks="密码到期时间">
                <constraints nullable="true"/>
            </column>
            <column name="Avatar" type="varchar(256)" remarks="头像">
                <constraints nullable="true"/>
            </column>
            <column name="ThemeName" type="varchar(128)" remarks="网站主题名称">
                <constraints nullable="true"/>
            </column>
            <column name="NeedResetPwd" type="tinyint(1)" remarks="登录后是否需重置密码">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>