<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_report_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="Report" remarks="Report info table">
            <column name="ReportId" type="int" remarks="报表ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportName" type="varchar(255)" remarks="报表名称">
                <constraints nullable="false"/>
            </column>
            <column name="ReportDescription" type="varchar(255)" remarks="报表备注信息">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaId" type="int" remarks="报表模式ID">
                <constraints nullable="false"/>
            </column>
            <column name="ReportSchemaCategoryId" type="int" remarks="报表Schema类型ID">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateUserId" type="int" remarks="修改人ID">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="修改时间">
                <constraints nullable="true"/>
            </column>
            <column name="ReportDataSourceId" type="int" remarks="报表数据源ID">
                <constraints nullable="false"/>
            </column>
            <column name="MaxQueryInterval" type="int" remarks="报表数据源ID">
                <constraints nullable="true"/>
            </column>
            <column name="CreateUserId" type="int" remarks="创建报表的用户id">
                <constraints nullable="true"/>
            </column>
            <column name="Overt" type="tinyint" remarks="是否公开" defaultValue="1">
                <constraints nullable="true"/>
            </column>
            <column name="ColumnConfig" type="json" remarks="报表列配置">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Report" columnName="ReportId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>