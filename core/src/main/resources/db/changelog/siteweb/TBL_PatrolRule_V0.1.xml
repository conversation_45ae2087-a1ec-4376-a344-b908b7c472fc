<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_TBLPatrolRule_info" author="ChenPeiHong"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_PatrolRule" remarks="TBL_PatrolRule info table">
            <column name="RuleId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RuleName" type="varchar(255)" remarks="规则名">
                <constraints nullable="false"/>
            </column>
            <column name="LimitDown" type="double" remarks="下限">
                <constraints nullable="true"/>
            </column>
            <column name="LimitDownCalOpId" type="int" remarks="下限比较符">
                <constraints nullable="false"/>
            </column>
            <column name="LimitUp" type="double" remarks="上限">
                <constraints nullable="true"/>
            </column>
            <column name="LimitUpCalOpId" type="int" remarks="上限比较符">
                <constraints nullable="false"/>
            </column>
            <column name="UnitId" type="int" remarks="单位">
                <constraints nullable="true"/>
            </column>
            <column name="WarningLevelId" type="int" remarks="预警等级">
                <constraints nullable="false"/>
            </column>
            <column name="ByPercentage" type="int" remarks="百分比">
                <constraints nullable="false"/>
            </column>
            <column name="RatedValue" type="double" remarks="比例值">
                <constraints nullable="true"/>
            </column>
            <column name="BaseEquipmentId" type="int" remarks="基类设备Id">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="decimal(12,0)" remarks="基类Id">
                <constraints nullable="true"/>
            </column>
            <column name="Note" type="varchar(255)" remarks="备注">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentLogicClassId" type="int" remarks="标准化设备Id">
                <constraints nullable="true"/>
            </column>
            <column name="StandardDicId" type="int" remarks="标准化Id">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="TBL_PatrolRule" columnName="RuleId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>