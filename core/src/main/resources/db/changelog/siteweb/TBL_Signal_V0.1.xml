<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblSignal_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Signal" remarks="tblSignal info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int" remarks="信号ID">
                <constraints nullable="false"/>
            </column>
            <column name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="Visible" type="tinyint(1)" remarks="是否可见">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="SignalName" type="varchar(128)" remarks="信号名">
                <constraints nullable="false"/>
            </column>
            <column name="SignalCategory" type="int" remarks="信号分类">
                <constraints nullable="false"/>
            </column>
            <column name="SignalType" type="int" remarks="信号类型">
                <constraints nullable="false"/>
            </column>
            <column name="ChannelNo" type="int" remarks="通道号">
                <constraints nullable="false"/>
            </column>
            <column name="ChannelType" type="int" remarks="通道类型">
                <constraints nullable="false"/>
            </column>
            <column name="Expression" type="varchar(1024)" remarks="计算表达式">
                <constraints nullable="true"/>
            </column>
            <column name="DataType" type="int" remarks="信号类型">
                <constraints nullable="true"/>
            </column>
            <column name="ShowPrecision" type="varchar(20)" remarks="显示精度">
                <constraints nullable="true" />
            </column>
            <column name="Unit" type="varchar(10)" remarks="信号单位">
                <constraints nullable="true"/>
            </column>
            <column name="StoreInterval" type="double" remarks="存储周期">
                <constraints nullable="true"/>
            </column>
            <column name="AbsvalueThreshold" type="double" remarks="绝对值阈值">
                <constraints nullable="true"/>
            </column>
            <column name="PercentThreshold" type="double" remarks="百分比阈值（注：2 即为2%）">
                <constraints nullable="true"/>
            </column>
            <column name="StaticsPeriod" type="int" remarks="统计周期">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="bigint" remarks="信号基类ID">
                <constraints nullable="true"/>
            </column>
            <column name="ChargeStoreInterval" type="double" remarks="放电存储周期">
                <constraints nullable="true"/>
            </column>
            <column name="ChargeAbsValue" type="double" remarks="放电存储阈值">
                <constraints nullable="true"/>
            </column>
            <column name="DisplayIndex" type="int" remarks="显示顺序">
                <constraints nullable="false"/>
            </column>
            <column name="MDBSignalId" type="int" remarks="MDB信号ID">
                <constraints nullable="true"/>
            </column>
            <column name="ModuleNo" type="int" remarks="模块号">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_Signal" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TBL_Signal" indexName="IDX_Signal_Id" unique="true">
            <column name="EquipmentTemplateId"></column>
            <column name="SignalId"></column>
        </createIndex>
    </changeSet>


</databaseChangeLog>