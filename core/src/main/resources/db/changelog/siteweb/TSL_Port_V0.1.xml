<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tslPort_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TSL_Port" remarks="tslPort info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PortId" type="int" remarks="端口ID">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitId" type="int" remarks="监控单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="PortNo" type="int" remarks="端口号">
                <constraints nullable="false"/>
            </column>
            <column name="PortName" type="varchar(128)" remarks="端口名">
                <constraints nullable="false"/>
            </column>
            <column name="PortType" type="int" remarks="端口类型">
                <constraints nullable="false"/>
            </column>
            <column name="Setting" type="varchar(255)"  remarks="端口设置">
                <constraints nullable="false"/>
            </column>
            <column name="PhoneNumber" type="varchar(20)" remarks="拨号号码">
                <constraints nullable="true"/>
            </column>
            <column name="LinkSamplerUnitId" type="int" remarks="关联采集单元ID">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TSL_Port" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TSL_Port" indexName="idx_Port_Id" unique="true">
            <column name="MonitorUnitId"></column>
            <column name="PortId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>