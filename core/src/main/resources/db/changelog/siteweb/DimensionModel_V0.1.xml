<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="DimensionModel" author="liaoximing"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DimensionModel" remarks="DimensionConfigure info table">
            <column name="DimensionModelId" type="int" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DimensionModelCategory" type="int" remarks="模型类型">
                <constraints nullable="true" />
            </column>
            <column name="DimensionModelName" type="varchar(128)" remarks="模型名称">
                <constraints nullable="true" />
            </column>
            <column name="DimensionModelFile" type="varchar(128)" remarks="模型文件">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="DimensionModel" columnName="DimensionModelId" incrementBy="1"
                          columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>