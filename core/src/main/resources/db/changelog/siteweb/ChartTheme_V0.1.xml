<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_charttheme_info" author="Liu.Yandong.Hanks">
        <createTable tableName="ChartTheme" remarks="ChartTheme info table">
            <column name="themeId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="themeName" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="themeCode" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="themeData" type="json">
                <constraints nullable="false"/>
            </column>
            <column name="themeDefault" type="TINYINT(1)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ChartTheme" columnName="ThemeId" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="ChartTheme" indexName="ChartTheme_ThemeName" unique="true">
            <column name="themeCode"></column>
        </createIndex>
    </changeSet>


</databaseChangeLog>