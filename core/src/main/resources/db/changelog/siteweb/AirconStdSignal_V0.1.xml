<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_stdsignal" author="hxh">
        <createTable tableName="Aircon_StdSignal"
                     remarks="aircon stdSignal">
            <column name="TypeId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_StdSignal_Table"/>
            </column>
            <column name="StdSignalId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_StdSignal_Table"/>
            </column>
            <column name="StdSignalName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="StdSignalUnit" type="varchar(16)">
                <constraints nullable="true"/>
            </column>
            <column name="StdSignalRemark" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="StdSignalType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="NeedShow" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CommandColor" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="BusinessTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="MapRequirement" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>