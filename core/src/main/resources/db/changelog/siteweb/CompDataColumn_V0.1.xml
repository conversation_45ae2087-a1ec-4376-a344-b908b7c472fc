<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_compdatacolumn_info" author="psx">
        <createTable tableName="CompDataColumn" remarks="compdatacolumn info table">
            <column name="CompDataColumnId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CompDataTableId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ColumnName" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
            <column name="ColumnField" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
            <column name="ValueType" type="tinyint">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="CompDataColumn" columnName="CompDataColumnId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>