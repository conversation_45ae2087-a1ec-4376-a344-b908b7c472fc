-- 空调标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 1, '工作电流', 'A', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 2, '温度', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 3, '湿度', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 4, '开机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 5, '关机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 6, '是否制热模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 7, '是否制冷模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 8, '工作异常告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 9, '压缩机电流异常告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 10, '空调防盗告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 11, '温度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 12, '温湿度传感器故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 13, '远程开机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 14, '远程关机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 15, '设定制热模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 16, '设定制冷模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 17, '工作温度设定值', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 18, '运行温度设定', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 19, '遥控风扇转速', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 20, '是否变频空调', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 21, '工作频率上限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 22, '工作频率下限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 23, '空调频率', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 1, '专用空调温度', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 2, '专用空调湿度', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 3, '开机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 4, '关机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 5, '温度设定值', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 6, '湿度设定值', '%', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 7, '空气流量过低告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 8, '温度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 9, '压缩机1高压告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 10, '压缩机2高压告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 11, '压缩机1低压告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 12, '压缩机2低压告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 13, '压缩机1过热告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 14, '压缩机2过热告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 15, '压缩机1过载告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 16, '压缩机2过载告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 17, '系统告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 18, '加湿器告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 19, '加热器告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 20, '溢水告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 21, '外接水浸告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 22, '电加热过温告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 23, '远程关机故障', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 24, '锁机故障', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 25, '水流量丢失告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 26, '风机故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 27, '远程开机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 28, '远程关机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 29, '再启动模式', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 30, '温度值设定', '℃', ' ', 4, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 31, '湿度值设定', '%Rh', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 32, '冷却比例带', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 33, '加热比例带', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 34, '加湿比例带', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 35, '除湿比例带', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 36, '核心机房空调性能预警', ' ', '设备性能预警', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 37, '是否变频空调', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 38, '工作频率上限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 39, '工作频率下限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 40, '空调频率', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 1, '空调1温度', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 2, '空调1湿度', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 3, '空调1开机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 4, '空调1关机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 5, '空调1是否制热模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 6, '空调1是否制冷模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 7, '空调1工作异常告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 8, '空调1温度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 9, '空调1温湿度传感器故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 10, '空调1远程开机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 11, '空调1远程关机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 12, '空调1设定制热模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 13, '空调1设定制冷模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 14, '空调1工作温度设定值', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 15, '空调1运行温度设定', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 16, '空调1遥控风扇转速', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 17, '空调2温度', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 18, '空调2湿度', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 19, '空调2开机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 20, '空调2关机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 21, '空调2是否制热模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 22, '空调2是否制冷模式', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 23, '空调2工作异常告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 24, '空调2温度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 25, '空调2温湿度传感器故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 26, '空调2远程开机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 27, '空调2远程关机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 28, '空调2设定制热模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 29, '空调2设定制冷模式', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 30, '空调2工作温度设定值', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 31, '空调2运行温度设定', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 32, '空调2遥控风扇转速', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 33, '空调1是否变频空调', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 34, '空调1工作频率上限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 35, '空调1工作频率下限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 36, '空调1频率', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 37, '空调2是否变频空调', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 38, '空调2工作频率上限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 39, '空调2工作频率下限', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 40, '空调2频率', '', ' ', 1, 1, 0, 0, 0);

-- 温湿度采集设备标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 1, '环境温度', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 2, '环境湿度', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 3, '水浸告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 4, '烟雾告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 5, '火情告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 6, '红外告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 7, '温度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 8, '温度过低告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 9, '湿度过高告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 10, '湿度过低告警', ' ', ' ', 5, 1, 0, 0, 0);

-- 风机设备标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 1, '室内温度', '℃', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 2, '室外温度', '℃', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 3, '室内传感器故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 4, '室外传感器故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 5, '进风风机故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 6, '出风风机故障告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 7, '风门卡滞告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 8, '过滤器脏堵告警', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 9, '强起风扇温度', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 10, '风扇开启温度', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 11, '风扇关闭温度', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 12, '室内外温差控制', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 13, '系统开机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 14, '系统关机', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 15, '开机状态', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 16, '关机状态', ' ', ' ', 2, 1, 0, 0, 2);

-- 标准信号的类型
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (1, '模拟', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (2, '状态', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (3, '遥控', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (4, '遥调', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (5, '告警', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (6, '常量', '');

-- 标准空调类型
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (1, '普通空调', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (2, '专用空调', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (3, '一拖二空调', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (4, '温湿度采集设备', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (5, '风机设备', '');
