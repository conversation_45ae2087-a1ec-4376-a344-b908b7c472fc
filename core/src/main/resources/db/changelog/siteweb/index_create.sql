-- 宏观操作日志表需要加日志
create index IDX_configchangemacrolog_1 on tbl_configchangemacrolog(UpdateTime desc, ConfigId);
-- 数据字典表需要增加索引
create index IDX_DataItem_EntryId on tbl_dataitem(entryId);

create index IDX_MonitorUnit_StationId on tsl_monitorunit(stationId);

create index IDX_Port_MonitorUnitId on tsl_port(MonitorUnitId,PortId);

create index IDX_SamplerUnit_MonitorUnitId on tsl_samplerunit(MonitorUnitId,PortId,SamplerUnitId);

create index IDX_EquipmentMaintain_ID on tbl_equipmentmaintain(stationId,equipmentId);

create index IDX_ResourceStructure_1 ON ResourceStructure(structureTypeId, OriginParentId, OriginId);

create index IDX_Equipment_ResourceStructureId  ON tbL_equipment(ResourceStructureId);

create index IDX_Equipment_StationId  ON tbL_equipment(StationId, HouseId);

create  index IDX_ROLE_PermissionId ON rolepermissionmap(roleId, permissionId);

create index IDX_ReportTimingTaskManagementId_CreateTime on reporttimingtaskfile(ReportTimingTaskManagementId,CreateTime);

create index IDX_EquipmentTemplateId on tbl_Equipment(EquipmentTemplateId);

create index IDX_BaseTypeId on tbl_Signal(BaseTypeId);

create index IDX_Expired on tbl_rackmountrecord(Expired);