CREATE TABLE capacityattribute
(
    attributeid      serial NOT NULL PRIMARY KEY,
    basetypeid       integer,
    baseattributeid  integer,
    attributename    character varying(128),
    logictype        integer,
    description      character varying(256),
    objectid         integer,
    objecttypeid     integer,
    complexindex     integer,
    "minvalue"       double precision,
    "maxvalue"       double precision,
    ratedcapacity    double precision,
    defaultvalue     double precision,
    compensatefactor double precision,
    origincapacity   double precision,
    usedcapacity     double precision,
    freecapacity     double precision,
    percent          double precision,
    sampletime       character varying(40),
    unit             character varying(32),
    "precision"      integer
);
COMMENT ON COLUMN capacityattribute.basetypeid IS '容量基类id';
COMMENT ON COLUMN capacityattribute.baseattributeid IS '容量父级id';
COMMENT ON COLUMN capacityattribute.attributename IS '容量名称';
COMMENT ON COLUMN capacityattribute.logictype IS '计算方式 0被动更新 1指标计算';
COMMENT ON COLUMN capacityattribute.description IS '描述';
COMMENT ON COLUMN capacityattribute.objectid IS '对象id';
COMMENT ON COLUMN capacityattribute.objecttypeid IS '对象资源类型id';
COMMENT ON COLUMN capacityattribute.complexindex IS '指标ID（不可修改）';
COMMENT ON COLUMN capacityattribute."minvalue" IS '最小值';
COMMENT ON COLUMN capacityattribute."maxvalue" IS '最大值';
COMMENT ON COLUMN capacityattribute.ratedcapacity IS '额定容量';
COMMENT ON COLUMN capacityattribute.defaultvalue IS '默认值';
COMMENT ON COLUMN capacityattribute.compensatefactor IS '补偿因子';
COMMENT ON COLUMN capacityattribute.origincapacity IS '原始输入数据';
COMMENT ON COLUMN capacityattribute.usedcapacity IS '已使用的容量 计算公式: 原生数据 * 补偿因子 (originCapacity * compensateFactor)';
COMMENT ON COLUMN capacityattribute.freecapacity IS '空闲容量';
COMMENT ON COLUMN capacityattribute.percent IS '容量百分比';
COMMENT ON COLUMN capacityattribute.sampletime IS '更新时间';
COMMENT ON COLUMN capacityattribute.unit IS '单位';
COMMENT ON COLUMN capacityattribute."precision" IS '精度';

CREATE TABLE capacityattributequartzrecord
(
    recordid        serial NOT NULL PRIMARY KEY,
    baseattributeid integer,
    attributename   character varying(128),
    objectid        integer,
    objecttypeid    integer,
    ratedcapacity   double precision,
    usedcapacity    double precision,
    freecapacity    double precision,
    percent         double precision,
    createtime      timestamp
);
COMMENT ON COLUMN capacityattributequartzrecord.baseattributeid IS '容量基类id';
COMMENT ON COLUMN capacityattributequartzrecord.attributename IS '容量名称';
COMMENT ON COLUMN capacityattributequartzrecord.objectid IS '对象id';
COMMENT ON COLUMN capacityattributequartzrecord.objecttypeid IS '对象类型id';
COMMENT ON COLUMN capacityattributequartzrecord.ratedcapacity IS '额定容量';
COMMENT ON COLUMN capacityattributequartzrecord.usedcapacity IS '已使用容量';
COMMENT ON COLUMN capacityattributequartzrecord.freecapacity IS '空闲剩余容量';
COMMENT ON COLUMN capacityattributequartzrecord.percent IS '容量百分比(已使用/空闲剩余容量)';
COMMENT ON COLUMN capacityattributequartzrecord.createtime IS '创建时间';
CREATE INDEX idx_21596_idx_createtime ON capacityattributequartzrecord USING BTREE (createtime);

CREATE TABLE capacitybaseattribute
(
    baseattributeid   serial NOT NULL PRIMARY KEY,
    baseattributename character varying(128),
    basetypeid        integer,
    attributename     character varying(128),
    logictype         integer,
    unit              character varying(32),
    description       character varying(256)
);
COMMENT ON COLUMN capacitybaseattribute.baseattributename IS '容量基类属性名称';
COMMENT ON COLUMN capacitybaseattribute.basetypeid IS '所属容量基类ID';
COMMENT ON COLUMN capacitybaseattribute.attributename IS '容量分类属性名';
COMMENT ON COLUMN capacitybaseattribute.logictype IS '逻辑处理方式 0被动更新 1指标计算';
COMMENT ON COLUMN capacitybaseattribute.unit IS '容量单位';
COMMENT ON COLUMN capacitybaseattribute.description IS '描述';

CREATE TABLE capacitybasetype
(
    basetypeid   serial NOT NULL PRIMARY KEY,
    basetypename character varying(128),
    description  character varying(256)
);
COMMENT ON COLUMN capacitybasetype.basetypename IS '容量基类名称';
COMMENT ON COLUMN capacitybasetype.description IS '描述';

CREATE TABLE computerrack
(
    computerrackid      serial NOT NULL PRIMARY KEY,
    computerrackname    character varying(256),
    computerracknumber  character varying(256),
    resourcestructureid integer,
    "position"          character varying(256),
    customer            character varying(256),
    business            character varying(256),
    starttime           timestamp,
    remark              character varying(510)
);
CREATE UNIQUE INDEX idx_21692_computerracknumber ON computerrack USING BTREE (computerracknumber);

CREATE TABLE computerrackequipmentemap
(
    computerrackequipmentmapid serial NOT NULL PRIMARY KEY,
    computerrackid             integer,
    equipmentid                integer
);
COMMENT ON COLUMN computerrackequipmentemap.computerrackid IS '机架id';
COMMENT ON COLUMN computerrackequipmentemap.equipmentid IS '设备id';

CREATE TABLE computerracksignalmap (
    computerRackSignalMapId SERIAL PRIMARY KEY,
    computerRackId INTEGER UNIQUE NOT NULL,
    openExpression VARCHAR(512) NULL,
    powerExpression VARCHAR(512) NULL
);

CREATE TABLE itdevice
(
    itdeviceid      serial NOT NULL PRIMARY KEY,
    serialnumber    character varying(128),
    itdevicename    character varying(128),
    itdevicemodelid integer,
    computerrackid  integer,
    uindex          integer,
    customer        character varying(128),
    business        character varying(128),
    purchasedate    timestamp,
    launchdate      timestamp,
    remark          character varying(510),
    assetdeviceid   integer,
    ipaddr          character varying(128)
);
COMMENT ON COLUMN itdevice.assetdeviceid IS 'IT设备和资产绑定';

CREATE TABLE itdevicemodel
(
    itdevicemodelid   serial NOT NULL PRIMARY KEY,
    itdevicemodelname character varying(128),
    unitheight        integer,
    manufactor        character varying(128),
    model             character varying(128),
    brand             character varying(128),
    length            double precision,
    width             double precision,
    height            double precision,
    ratepower         double precision,
    ratecooling       double precision,
    rateweight        double precision,
    modelfile         character varying(510),
    categoryid        integer,
    assetdeviceid     integer
);
COMMENT ON COLUMN itdevicemodel.categoryid IS 'IT设备模型类型(用于机架管理统计)';
COMMENT ON COLUMN itdevicemodel.assetdeviceid IS 'IT设备和资产绑定';

CREATE TABLE tbl_rackmountrecord
(
    id           serial  NOT NULL PRIMARY KEY,
    rackid       integer NOT NULL,
    rackdeviceid integer NOT NULL,
    rackposition integer NOT NULL,
    operatetime  timestamp NOT NULL,
    operatestate integer NOT NULL,
    expired      integer
);
CREATE INDEX idx_23748_rackmountrecord_expired_index ON tbl_rackmountrecord USING BTREE (expired);

CREATE TABLE tbl_udevice
(
    udeviceid     serial NOT NULL PRIMARY KEY,
    udevicenumber character varying(256),
    isonline      integer,
    rackid        integer,
    modulecnt     integer,
    utagcnt       integer,
    ipaddr        character varying(90),
    swequipmentid integer,
    createtime    timestamp,
    updatetime    timestamp
);
COMMENT ON COLUMN tbl_udevice.udevicenumber IS 'U位管理设备唯一标识码';
COMMENT ON COLUMN tbl_udevice.isonline IS '是否在线';
COMMENT ON COLUMN tbl_udevice.rackid IS '对应机架Id';
COMMENT ON COLUMN tbl_udevice.modulecnt IS '额定U位';
COMMENT ON COLUMN tbl_udevice.utagcnt IS '标签数';
COMMENT ON COLUMN tbl_udevice.ipaddr IS 'IP地址';
COMMENT ON COLUMN tbl_udevice.swequipmentid IS 'SiteWeb设备Id(U位管理与SW采集设备关联关系)';
COMMENT ON COLUMN tbl_udevice.createtime IS '创建时间';
COMMENT ON COLUMN tbl_udevice.updatetime IS '更新时间';
CREATE UNIQUE INDEX idx_24164_udevicenumber ON tbl_udevice USING BTREE (udevicenumber);
CREATE UNIQUE INDEX idx_24164_rackid ON tbl_udevice USING BTREE (rackid);

CREATE TABLE tbl_utag
(
    utagid     serial NOT NULL PRIMARY KEY,
    tagvalue   character varying(90),
    asserid    integer,
    isonline   integer,
    udeviceid  integer,
    uposition  integer,
    createtime timestamp,
    updatetime timestamp
);
COMMENT ON COLUMN tbl_utag.tagvalue IS '标签的唯一识别码';
COMMENT ON COLUMN tbl_utag.asserid IS '绑定资产Id';
COMMENT ON COLUMN tbl_utag.isonline IS '是否在线';
COMMENT ON COLUMN tbl_utag.udeviceid IS 'U位管理Id';
COMMENT ON COLUMN tbl_utag.uposition IS '所处U位';
COMMENT ON COLUMN tbl_utag.createtime IS '创建时间';
COMMENT ON COLUMN tbl_utag.updatetime IS '更新时间';
CREATE UNIQUE INDEX idx_24181_tagvalue ON tbl_utag USING BTREE (tagvalue);
CREATE UNIQUE INDEX idx_24181_asserid ON tbl_utag USING BTREE (asserid);

CREATE TABLE yd_tmputagmap
(
    tagvalue    character varying(510) NOT NULL,
    asserid     integer,
    equipmentid integer,
    signalid    integer
);
CREATE INDEX idx_24650_yd_tmputagmapidx1 ON yd_tmputagmap USING BTREE (tagvalue);