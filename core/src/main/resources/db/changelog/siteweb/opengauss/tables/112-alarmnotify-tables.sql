CREATE TABLE alarmnotifyconfig
(
    alarmnotifyconfigid serial                  NOT NULL PRIMARY KEY,
    configname          character varying(256)  NOT NULL,
    usedstatus          integer                 NOT NULL,
    contenttemplate     character varying(2000) NOT NULL,
    notifydelay         integer,
    layout              text,
    description         character varying(510),
    updatetime          timestamp,
    departmentid        integer DEFAULT 0
);

CREATE TABLE alarmnotifyelement
(
    elementid        serial                 NOT NULL PRIMARY KEY,
    elementname      character varying(256) NOT NULL,
    elementtype      character varying(128)  NOT NULL,
    inputnodescount  integer                NOT NULL,
    outputnodescount integer                NOT NULL,
    icon             character varying(128),
    expression       character varying(1000),
    visible          integer                NOT NULL,
    sortindex        integer                NOT NULL,
    description      character varying(510)
);

CREATE TABLE alarmnotifyelementconfig
(
    alarmnotifyelementconfigid serial  NOT NULL PRIMARY KEY,
    alarmnotifyconfigid        integer NOT NULL,
    elementid                  integer NOT NULL,
    expression                 character varying(2000),
    extendfield1               character varying(1000),
    extendfield2               character varying(1000),
    extendfield3               character varying(1000)
);

CREATE TABLE alarmnotifyfiltercondition
(
    filterconditionid   integer                NOT NULL PRIMARY KEY,
    filterconditionname character varying(256) NOT NULL
);

CREATE TABLE alarmnotifyfilterrule
(
    alarmnotifyfilterruleid serial                   NOT NULL PRIMARY KEY,
    alarmnotifyconfigid     integer                  NOT NULL,
    filterconditionid       integer                  NOT NULL,
    filterparameter         character varying(20000) NOT NULL
);

CREATE TABLE alarmnotifygatewayservice
(
    alarmnotifygatewayserviceid serial NOT NULL PRIMARY KEY,
    elementid                   integer,
    gatewayserviceurl           character varying(128),
    description                 character varying(512)
);
COMMENT ON COLUMN alarmnotifygatewayservice.elementid IS '发送方式id';
COMMENT ON COLUMN alarmnotifygatewayservice.gatewayserviceurl IS '网关服务url';
COMMENT ON COLUMN alarmnotifygatewayservice.description IS '备注';

CREATE TABLE alarmnotifygatewayserviceconfig
(
    alarmnotifygatewayserviceconfigid serial NOT NULL PRIMARY KEY,
    alarmnotifyconfigid               integer,
    alarmnotifygatewayserviceid       integer
);
COMMENT ON COLUMN alarmnotifygatewayserviceconfig.alarmnotifyconfigid IS '策略id';
COMMENT ON COLUMN alarmnotifygatewayserviceconfig.alarmnotifygatewayserviceid IS 'AlarmNotifyGatewayServiceId';


CREATE TABLE alarmnotifynode
(
    nodeid                     serial                NOT NULL PRIMARY KEY,
    alarmnotifyelementconfigid integer               NOT NULL,
    nodedirection              character varying(20) NOT NULL,
    nodetype                   character varying(128) NOT NULL,
    nodeindex                  integer               NOT NULL,
    nodetag                    character varying(510),
    expression                 character varying(2000)
);

CREATE TABLE alarmnotifyrecord
(
    id                  serial                  NOT NULL PRIMARY KEY,
    alarmnotifyconfigid integer                 NOT NULL,
    stationid           integer                 NOT NULL,
    equipmentid         integer                 NOT NULL,
    eventid             integer                 NOT NULL,
    eventconditionid    integer                 NOT NULL,
    sequenceid          character varying(256)  NOT NULL,
    eventseverityid     integer                 NOT NULL,
    content             character varying(2000) NOT NULL,
    alarmstarttime      timestamp NOT NULL,
    sendtime            timestamp NOT NULL,
    receiver            character varying(510)  NOT NULL,
    sendtype            character varying(40)   NOT NULL,
    sendresult          character varying(200)  NOT NULL
);

CREATE TABLE alarmnotifysegment
(
    segmentid             serial  NOT NULL PRIMARY KEY,
    inputelementconfigid  integer NOT NULL,
    inputnodeid           integer NOT NULL,
    outputelementconfigid integer NOT NULL,
    outputnodeid          integer NOT NULL
);