CREATE TABLE tbl_standardback
(
    entrycategory       integer NOT NULL,
    equipmenttemplateid integer NOT NULL,
    entryid             integer NOT NULL,
    eventconditionid    integer NOT NULL,
    signalname          character varying(256),
    storeinterval       double precision,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    eventname           character varying(256),
    eventseverity       integer,
    startcomparevalue   double precision,
    startdelay          integer,
    standardname        integer,
    meanings            character varying(510),
    controlname         character varying(256)
);
ALTER TABLE ONLY tbl_standardback ADD CONSTRAINT idx_23978_primary PRIMARY KEY (entrycategory, entryid, equipmenttemplateid, eventconditionid);

CREATE TABLE tbl_standarddic
(
    standarddicid       integer                NOT NULL PRIMARY KEY,
    signalstandardname  character varying(510) NOT NULL,
    signalstandardid    integer                NOT NULL,
    equipmentlogicclass character varying(510) NOT NULL,
    storeinterval       integer,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    standardname        character varying(510),
    eventseverity       integer,
    eventlogicclass     character varying(510),
    eventclass          character varying(510),
    netmanageid         character varying(510),
    equipmentaffect     character varying(510),
    businessaffect      character varying(510),
    comparevalue        character varying(256),
    startdelay          character varying(128),
    controlstandardname character varying(510),
    controlstandardid   integer,
    controltype         integer,
    unit                character varying(256),
    description         character varying(510),
    meanings            character varying(510),
    nodetype            integer
);

CREATE TABLE tbl_standarddiccontrol
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(256) NOT NULL,
    controllogicclassid   integer,
    controllogicclass     character varying(256),
    controlstandardname   character varying(510),
    netmanageid           character varying(510),
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(510),
    extendfiled1          text,
    extendfiled2          text
);
ALTER TABLE ONLY tbl_standarddiccontrol ADD CONSTRAINT idx_23988_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standarddicevent
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(256) NOT NULL,
    eventlogicclassid     integer,
    eventlogicclass       character varying(256),
    eventclass            character varying(510),
    eventstandardname     character varying(510),
    netmanageid           character varying(510),
    eventseverity         integer,
    comparevalue          character varying(256),
    startdelay            character varying(128),
    meanings              character varying(510),
    equipmentaffect       character varying(510),
    businessaffect        character varying(510),
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(510),
    extendfiled1          text,
    extendfiled2          text,
    extendfiled3          text
);
ALTER TABLE ONLY tbl_standarddicevent ADD CONSTRAINT idx_23993_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standarddicsig
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(256) NOT NULL,
    signallogicclassid    integer,
    signallogicclass      character varying(256),
    signalstandardname    character varying(510) NOT NULL,
    netmanageid           character varying(510),
    storeinterval         integer,
    absvaluethreshold     double precision,
    statisticsperiod      integer,
    percentthreshold      double precision,
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(510),
    extendfiled1          text,
    extendfiled2          text
);
ALTER TABLE ONLY tbl_standarddicsig ADD CONSTRAINT idx_23998_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standardrule
(
    standardruleid     serial  NOT NULL PRIMARY KEY,
    standardtemplateid integer NOT NULL,
    signalname         character varying(256),
    eventname          character varying(256),
    expression         character varying(256),
    meanings           character varying(256),
    controlname        character varying(256),
    standarddicid      integer
);

CREATE TABLE tbl_standardtemplate
(
    standardtemplateid   integer                NOT NULL,
    standardtemplatename character varying(510) NOT NULL,
    stationcategory      integer                NOT NULL,
    equipmentcategory    integer                NOT NULL,
    vendor               character varying(510) NOT NULL,
    equipmentmodel       character varying(510) NOT NULL,
    monitormodule        character varying(510)
);

CREATE TABLE tbl_standardtemplatemap
(
    equipmenttemplateid integer NOT NULL,
    stationcategory     integer NOT NULL,
    standardtemplateid  integer NOT NULL
);

CREATE TABLE tbl_standardtype
(
    standardid    integer                NOT NULL PRIMARY KEY,
    standardname  character varying(510) NOT NULL,
    standardalias character varying(510) NOT NULL,
    remark        character varying(510)
);