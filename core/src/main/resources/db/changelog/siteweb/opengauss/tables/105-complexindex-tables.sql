CREATE TABLE complexindex
(
    complexindexid           serial NOT NULL PRIMARY KEY,
    complexindexname         character varying(256),
    complexindexdefinitionid integer,
    objectid                 integer,
    calccron                 character varying(256),
    calctype                 integer,
    aftercalc                text,
    savecron                 character varying(256),
    expression               text,
    unit                     character varying(256),
    accuracy                 character varying(256),
    objecttypeid             integer,
    remark                   character varying(256),
    label                    character varying(256),
    businesstypeid           integer,
    checkexpression          character varying(2048)
);

CREATE TABLE complexindexbusinessobjectmap
(
    businessobjectmapid      serial  NOT NULL PRIMARY KEY,
    sceneid                  integer NOT NULL,
    businesstypeid           integer NOT NULL,
    objecttypeid             integer NOT NULL,
    complexindexdefinitionid integer NOT NULL
);

CREATE TABLE complexindexbusinesstype
(
    businesstypeid   serial                NOT NULL PRIMARY KEY,
    businesstypename character varying(100) NOT NULL,
    parentid         integer               NOT NULL,
    description      character varying(510)
);

CREATE TABLE complexindexdefinition
(
    complexindexdefinitionid   serial                NOT NULL PRIMARY KEY,
    complexindexdefinitionname character varying(100) NOT NULL,
    calccron                   character varying(256),
    calctype                   integer,
    aftercalc                  character varying(256),
    savecron                   character varying(256),
    expression                 character varying(256),
    unit                       character varying(256),
    accuracy                   character varying(256),
    startstatus                integer,
    icon                       character varying(256),
    checkexpression            character varying(256),
    description                character varying(510)
);

CREATE TABLE complexindexfunction
(
    functionid          serial NOT NULL PRIMARY KEY,
    functionexpression  character varying(256),
    functiondescription character varying(256),
    functionname        character varying(256),
    remark              character varying(256)
);