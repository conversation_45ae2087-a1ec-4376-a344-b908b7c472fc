CREATE TABLE tbl_equipmentresource
(
    stationid         integer NOT NULL,
    equipmentid       integer NOT NULL,
    houseid           integer,
    equipmentcategory integer NOT NULL,
    vendor            character varying(510),
    useddate          timestamp,
    equipmentmodule   character varying(256),
    localnetwork      character varying(510),
    description       character varying(510),
    extendfield1      character varying(510),
    extendfield2      character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresource ADD CONSTRAINT idx_23039_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourceaircon
(
    stationid       integer NOT NULL,
    equipmentid     integer NOT NULL,
    aircontype      integer,
    coolingcapacity double precision,
    description     character varying(510),
    extendfield1    character varying(510),
    extendfield2    character varying(510),
    extendfield3    character varying(510),
    extendfield4    character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourceaircon ADD CONSTRAINT idx_23044_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourcebattery
(
    stationid          integer NOT NULL,
    equipmentid        integer NOT NULL,
    batterytype        integer,
    ratedoutputvoltage double precision,
    ratedcapacity      double precision,
    batteryvoltage     integer,
    batterycapacity    double precision,
    batterycounts      integer,
    description        character varying(510),
    extendfield1       character varying(510),
    extendfield2       character varying(510),
    extendfield3       character varying(510),
    extendfield4       character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourcebattery ADD CONSTRAINT idx_23049_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourcedcpower
(
    stationid         integer NOT NULL,
    equipmentid       integer NOT NULL,
    modulecapacity    double precision,
    modulenums        integer,
    batterynums       integer,
    batterycounts     integer,
    batterycapacity   double precision,
    batteryvoltage    integer,
    supplyinfo        character varying(510),
    systemcapacities  double precision,
    batterycapacities double precision,
    description       character varying(510),
    extendfield1      character varying(510),
    extendfield2      character varying(510),
    extendfield3      character varying(510),
    extendfield4      character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourcedcpower ADD CONSTRAINT idx_23054_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourcegenset
(
    stationid        integer NOT NULL,
    equipmentid      integer NOT NULL,
    ratedoutputpower double precision,
    description      character varying(510),
    extendfield1     character varying(510),
    extendfield2     character varying(510),
    extendfield3     character varying(510),
    extendfield4     character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourcegenset ADD CONSTRAINT idx_23059_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourceitem
(
    entryitemid   integer                NOT NULL PRIMARY KEY,
    entryid       integer                NOT NULL,
    entryvalue    character varying(510),
    itemid        integer                NOT NULL,
    itemvalue     character varying(510) NOT NULL,
    parententryid integer DEFAULT 0      NOT NULL,
    parentitemid  integer DEFAULT 0      NOT NULL,
    description   character varying(510),
    extendfield1  character varying(510),
    extendfield2  character varying(510)
);

CREATE TABLE tbl_equipmentresourcepower
(
    stationid         integer NOT NULL,
    equipmentid       integer NOT NULL,
    powertype         integer,
    modulecapacity    integer,
    modulenums        integer,
    batterynums       integer,
    batterycounts     integer,
    batterycapacity   double precision,
    batteryvoltage    integer,
    supplyinfo        character varying(510),
    systemcapacities  double precision,
    batterycapacities double precision,
    description       character varying(510),
    extendfield1      character varying(510),
    extendfield2      character varying(510),
    extendfield3      character varying(510),
    extendfield4      character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourcepower ADD CONSTRAINT idx_23071_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourcetransformer
(
    stationid       integer NOT NULL,
    equipmentid     integer NOT NULL,
    transformertype integer,
    ratedcapacity   double precision,
    description     character varying(510),
    extendfield1    character varying(510),
    extendfield2    character varying(510),
    extendfield3    character varying(510),
    extendfield4    character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourcetransformer ADD CONSTRAINT idx_23076_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentresourceups
(
    stationid       integer NOT NULL,
    equipmentid     integer NOT NULL,
    upstype         integer,
    ratedcapacity   double precision,
    modulenums      integer,
    batterynums     integer,
    batterycounts   integer,
    batterycapacity double precision,
    batteryvoltage  integer,
    supplyinfo      character varying(510),
    description     character varying(510),
    extendfield1    character varying(510),
    extendfield2    character varying(510),
    extendfield3    character varying(510),
    extendfield4    character varying(510)
);
ALTER TABLE ONLY tbl_equipmentresourceups ADD CONSTRAINT idx_23081_primary PRIMARY KEY (equipmentid, stationid);