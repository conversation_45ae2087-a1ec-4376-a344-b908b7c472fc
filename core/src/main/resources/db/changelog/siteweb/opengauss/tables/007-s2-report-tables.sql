CREATE TABLE tbl_report
(
    reportid         integer                NOT NULL PRIMARY KEY,
    reportname       character varying(510) NOT NULL,
    description      character varying(510),
    reportfileid     character varying(510),
    reportfilename   character varying(510),
    previewimagename character varying(510),
    createuserid     integer,
    createusername   character varying(510),
    createtime       timestamp,
    updateuserid     integer,
    updateusername   character varying(510),
    updatetime       timestamp,
    version          character varying(100)
);

CREATE TABLE tbl_reportbrowehistory
(
    historyid      integer NOT NULL,
    userid         integer,
    reportid       integer,
    viewcount      integer,
    lastbrowsetime timestamp
);

CREATE TABLE tbl_reportchart
(
    reportid    integer NOT NULL,
    chartid     integer NOT NULL,
    charttype   character varying(40),
    chartname   character varying(256),
    xaxis       character varying(256),
    yaxis       character varying(256),
    serials     character varying(256),
    serialsname character varying(256)
);
ALTER TABLE ONLY tbl_reportchart ADD CONSTRAINT idx_23768_primary PRIMARY KEY (chartid, reportid);

CREATE TABLE tbl_reportgroup
(
    reportgroupid integer                NOT NULL,
    groupname     character varying(510) NOT NULL,
    userid        integer,
    grouptype     integer DEFAULT 0,
    description   character varying(510)
);

CREATE TABLE tbl_reportgroupmap
(
    reportid      integer NOT NULL,
    reportgroupid integer NOT NULL
);

CREATE TABLE tbl_reportparameter
(
    parameterid     integer                NOT NULL PRIMARY KEY,
    parametername   character varying(256),
    parameterkey    character varying(256),
    procedurename   character varying(256),
    parameterstring text,
    parameterfield  character varying(256),
    parametertype   character varying(40),
    parameterfrom   character varying(40),
    description     character varying(510)
);

CREATE TABLE tbl_reportparametermap
(
    reportid    integer NOT NULL,
    parameterid integer NOT NULL
);
ALTER TABLE ONLY tbl_reportparametermap ADD CONSTRAINT idx_23787_primary PRIMARY KEY (parameterid, reportid);

CREATE TABLE tbl_reportprocedure
(
    reportid        integer                NOT NULL PRIMARY KEY,
    procedurename   character varying(256) NOT NULL,
    parameterstring text,
    reportcolumn    text,
    reporttype      integer,
    description     character varying(510)
);

CREATE TABLE tbl_reportquery
(
    queryid     integer,
    reportid    integer,
    name        character varying(510),
    taskid      integer,
    description character varying(510),
    querytime   timestamp
);

CREATE TABLE tbl_reportqueryparameter
(
    queryparameterid integer NOT NULL,
    queryid          integer,
    reportid         integer,
    parametername    character varying(510),
    datatype         character varying(128),
    value            text
);

CREATE TABLE tbl_reportrole
(
    reportroleid integer               NOT NULL PRIMARY KEY,
    rolename     character varying(100) NOT NULL,
    description  character varying(510)
);

CREATE TABLE tbl_reportrolemap
(
    reportid     integer NOT NULL,
    reportroleid integer NOT NULL
);
ALTER TABLE ONLY tbl_reportrolemap ADD CONSTRAINT idx_23808_primary PRIMARY KEY (reportid, reportroleid);

CREATE TABLE tbl_reportroleusermap
(
    reportroleid integer NOT NULL,
    userid       integer NOT NULL
);

CREATE TABLE tbl_reporttask
(
    taskid             integer                NOT NULL PRIMARY KEY,
    taskname           character varying(256) NOT NULL,
    description        character varying(510),
    reportid           integer                NOT NULL,
    starttime          timestamp,
    endtime            timestamp,
    outputtime         timestamp,
    outputinterval     integer,
    outputintervaltype integer,
    queryperiod        integer,
    queryperiodtype    integer,
    queryparameters    text                   NOT NULL,
    commandtext        character varying(510),
    commandtype        character varying(256),
    centertype         integer DEFAULT 0      NOT NULL,
    querycount         integer,
    emailout           integer DEFAULT 0      NOT NULL,
    emailurl           text,
    creator            integer,
    createtime         timestamp,
    lastupdatetime     timestamp
);

CREATE TABLE tbl_reporttaskfile
(
    fileid    serial NOT NULL PRIMARY KEY,
    taskid    integer,
    reportid  integer,
    filename  character varying(256),
    starttime timestamp,
    endtime   timestamp,
    querytime timestamp
);

CREATE TABLE tbl_reportusermap
(
    reportid integer,
    userid   integer
);

CREATE TABLE tbl_suitreport
(
    suitreportid   integer                NOT NULL,
    suitreportname character varying(510) NOT NULL,
    description    character varying(510),
    createuserid   integer,
    createusername character varying(510),
    createtime     timestamp,
    updateuserid   integer,
    updateusername character varying(510),
    updatetime     timestamp
);

CREATE TABLE tbl_suitreportmap
(
    suitreportid integer NOT NULL,
    reportid     integer NOT NULL
);

CREATE TABLE tbl_suitreportrolemap
(
    suitreportid integer NOT NULL,
    reportroleid integer NOT NULL
);