CREATE TABLE linkconfig
(
    linkconfigid    serial                 NOT NULL PRIMARY KEY,
    configname      character varying(256) NOT NULL,
    usedstatus      integer                NOT NULL,
    linkgroupid     integer                NOT NULL,
    linktriggertype integer                NOT NULL,
    cron            character varying(200),
    starttime       timestamp,
    endtime         timestamp,
    layout          text,
    description     character varying(510),
    updatetime      timestamp
);
COMMENT ON COLUMN linkconfig.cron IS 'corn表达式用于定时执行后台联动';

CREATE TABLE linkconfigtemplate
(
    linkconfigtemplateid serial                 NOT NULL PRIMARY KEY,
    templatename         character varying(256) NOT NULL,
    content              text                   NOT NULL,
    description          character varying(510)
);

CREATE TABLE linkelement
(
    elementid        serial                 NOT NULL PRIMARY KEY,
    elementname      character varying(256) NOT NULL,
    elementtype      character varying(128)  NOT NULL,
    inputnodescount  integer                NOT NULL,
    outputnodescount integer                NOT NULL,
    icon             character varying(128),
    expression       character varying(1000),
    visible          integer                NOT NULL,
    sortindex        integer                NOT NULL,
    description      character varying(510)
);

CREATE TABLE linkelementconfig
(
    linkelementconfigid serial  NOT NULL PRIMARY KEY,
    linkconfigid        integer NOT NULL,
    elementid           integer NOT NULL,
    expression          character varying(2000),
    extendfield1        character varying(1000),
    extendfield2        character varying(1000),
    extendfield3        character varying(1000)
);

CREATE TABLE linkgroup
(
    groupid     serial                 NOT NULL PRIMARY KEY,
    groupname   character varying(256) NOT NULL,
    description character varying(510)
);

CREATE TABLE linkinstance
(
    linkinstanceid serial                 NOT NULL PRIMARY KEY,
    linkconfigid   integer                NOT NULL,
    updatetime     timestamp,
    status         integer                NOT NULL,
    statusresult   character varying(256) NOT NULL,
    description    character varying(510)
);

CREATE TABLE linknode
(
    nodeid              serial                NOT NULL PRIMARY KEY,
    linkelementconfigid integer               NOT NULL,
    nodedirection       character varying(20) NOT NULL,
    nodetype            character varying(128) NOT NULL,
    nodeindex           integer               NOT NULL,
    nodetag             character varying(510),
    expression          character varying(1000)
);

CREATE TABLE linksegment
(
    segmentid                 serial  NOT NULL PRIMARY KEY,
    inputlinkelementconfigid  integer NOT NULL,
    inputnodeid               integer NOT NULL,
    outputlinkelementconfigid integer NOT NULL,
    outputnodeid              integer NOT NULL
);