CREATE TABLE activeeventfiltertemplate
(
    activeeventfiltertemplateid serial                  NOT NULL PRIMARY KEY,
    userid                      integer,
    filtertype                  character varying(256)  NOT NULL,
    templatename                character varying(510)  NOT NULL,
    content                     character varying(16000) NOT NULL,
    description                 character varying(510)
);

CREATE TABLE activeeventoperationlog
(
    activeeventoperationlogid bigserial NOT NULL,
    sequenceid       character varying(256),
    stationid        integer,
    equipmentid      integer,
    eventid          integer,
    eventconditionid integer,
    starttime        timestamp,
    operatorid       integer,
    operation        character varying(256),
    operationtime    timestamp,
    description      character varying(510)
);
COMMENT ON COLUMN activeeventoperationlog.sequenceid IS '流水号';
COMMENT ON COLUMN activeeventoperationlog.stationid IS '局站ID';
COMMENT ON COLUMN activeeventoperationlog.equipmentid IS '设备ID';
COMMENT ON COLUMN activeeventoperationlog.eventid IS '事件ID';
COMMENT ON COLUMN activeeventoperationlog.eventconditionid IS '事件条件ID';
COMMENT ON COLUMN activeeventoperationlog.starttime IS '开始时间';
COMMENT ON COLUMN activeeventoperationlog.operatorid IS '操作人ID';
COMMENT ON COLUMN activeeventoperationlog.operation IS '告警操作';
COMMENT ON COLUMN activeeventoperationlog.operationtime IS '操作时间';
COMMENT ON COLUMN activeeventoperationlog.description IS '备注';
CREATE INDEX idx_21329_idx_activeeventoperationlog_sequenceid ON activeeventoperationlog USING BTREE (sequenceid);
CREATE INDEX idx_21329_activeeventoperationlog_idx1 ON activeeventoperationlog USING BTREE (starttime, equipmentid, eventid, stationid);

CREATE TABLE activenotification
(
    alarmsequenceid        integer NOT NULL,
    birthtime              timestamp,
    stationid              integer,
    stationname            character varying(256),
    equipmentid            integer,
    equipmentname          character varying(256),
    eventid                integer,
    eventconditionid       integer,
    eventuniqueid          character varying(510),
    eventname              character varying(256),
    eventseverity          integer,
    severityname           character varying(100),
    eventstatus            integer NOT NULL,
    overturn               integer,
    meaning                character varying(256),
    starttime              timestamp,
    endtime                timestamp,
    confirmtime            timestamp,
    updatetimeformat       character varying(510),
    confirmuserid          integer,
    confirmusername        character varying(256),
    centername             character varying(256),
    stationstate           character varying(256),
    equipmentcategoryname  character varying(256),
    equipmentvendorname    character varying(256),
    equipmentlogiccategory character varying(256),
    logiccategory          character varying(256),
    sublogiccategory       character varying(256),
    infectiontoequipment   character varying(256),
    infectiontobusiness    character varying(256),
    standardalarmname      character varying(256),
    standardnameid         character varying(256),
    alarmcomment           character varying(256),
    netalarmid             character varying(256),
    notifyserverid         integer,
    notificationtype       integer,
    setting                character varying(510),
    notificationrecieverid integer,
    retrytimes             integer,
    eventfilterdelay       integer,
    notifyresult           integer,
    instructionid          character varying(256)
);
CREATE UNIQUE INDEX idx_21335_pk_activenotification_id ON activenotification USING BTREE (alarmsequenceid, eventstatus);

CREATE TABLE alarmmasklog
(
    id bigserial NOT NULL PRIMARY KEY,
    stationid           integer NOT NULL,
    equipmentid         integer NOT NULL,
    eventid             integer,
    resourcestructureid integer,
    userid              integer NOT NULL,
    operationtype       integer NOT NULL,
    operationtime       timestamp,
    timegroupcategory   integer,
    starttime           timestamp,
    endtime             timestamp,
    timegroupchars      character varying(510),
    comment             character varying(510)
);
CREATE INDEX idx_21429_alarmmasklog_idx1 ON alarmmasklog USING BTREE (operationtime, equipmentid, eventid);

CREATE TABLE allnotification
(
    alarmsequenceid         integer                NOT NULL,
    birthtime               timestamp,
    stationid               integer,
    stationname             character varying(256),
    equipmentid             integer,
    equipmentname           character varying(256),
    eventid                 integer,
    eventconditionid        integer,
    eventuniqueid           character varying(510) NOT NULL,
    eventname               character varying(256),
    eventseverity           integer,
    severityname            character varying(100),
    eventstatus             integer                NOT NULL,
    overturn                integer,
    meaning                 character varying(256),
    starttime               timestamp,
    endtime                 timestamp,
    confirmtime             timestamp,
    updatetimeformat        character varying(510),
    confirmuserid           integer,
    confirmusername         character varying(256),
    centername              character varying(256),
    stationstate            character varying(256),
    equipmentcategoryname   character varying(256),
    equipmentvendorname     character varying(256),
    equipmentlogiccategory  character varying(256),
    logiccategory           character varying(256),
    sublogiccategory        character varying(256),
    infectiontoequipment    character varying(256),
    infectiontobusiness     character varying(256),
    standardalarmname       character varying(256),
    standardnameid          character varying(256),
    alarmcomment            character varying(256),
    netalarmid              character varying(256),
    defaultstationgroupname character varying(256),
    notificationtype        integer                NOT NULL,
    stationcategoryid       integer,
    stationcategoryname     character varying(256)
);
ALTER TABLE ONLY allnotification ADD CONSTRAINT idx_21513_primary PRIMARY KEY (alarmsequenceid, eventuniqueid, eventstatus, notificationtype);
CREATE INDEX idx_21513_allnotification_idx1 ON allnotification USING BTREE (stationid, equipmentid, eventid, eventconditionid, eventstatus, starttime, notificationtype);
CREATE INDEX idx_21513_allnotification_idx2 ON allnotification USING BTREE (eventuniqueid, alarmsequenceid, eventstatus, starttime, notificationtype);

CREATE TABLE eventfilter
(
    eventfilterid   integer NOT NULL PRIMARY KEY,
    eventfiltername character varying(256),
    description     character varying(510),
    lastupdatedate  timestamp DEFAULT localtimestamp NOT NULL
);

CREATE TABLE eventfiltercondition
(
    eventfilterid          integer           NOT NULL,
    eventfilterconditionid integer           NOT NULL,
    eventfiltercombination text              NOT NULL,
    eventfiltersegment1    integer,
    eventfiltersegment2    integer,
    eventfiltersegment3    integer,
    eventfiltersegment4    integer,
    eventfiltersegment5    integer,
    eventfiltersegment6    integer,
    eventfiltersegment7    integer,
    eventfiltersegment8    integer,
    eventfiltersegment9    integer,
    eventfiltersegment10   integer,
    eventfiltersegment11   integer,
    eventfilterdelay       integer DEFAULT 0 NOT NULL,
    eventfiltercount       integer DEFAULT 0 NOT NULL,
    description            text,
    lastupdatedate         timestamp DEFAULT localtimestamp NOT NULL
);
ALTER TABLE ONLY eventfiltercondition ADD CONSTRAINT idx_21967_primary PRIMARY KEY (eventfilterid, eventfilterconditionid);

CREATE TABLE eventfiltermap
(
    eventfiltermemberid integer NOT NULL,
    eventfilterid       integer NOT NULL
);
ALTER TABLE ONLY eventfiltermap ADD CONSTRAINT idx_21975_primary PRIMARY KEY (eventfiltermemberid, eventfilterid);

CREATE TABLE eventfiltermember
(
    eventfiltermemberid   integer                NOT NULL PRIMARY KEY,
    eventfiltermembername character varying(256) NOT NULL,
    description           character varying(510),
    lastupdatedate        timestamp DEFAULT localtimestamp NOT NULL
);

CREATE TABLE eventnotifyreciever
(
    stationid              integer NOT NULL,
    equipmentid            integer NOT NULL,
    eventid                integer NOT NULL,
    eventseverity          integer NOT NULL,
    eventstate             integer NOT NULL,
    notifyreceiverid       integer NOT NULL,
    notifyreceivercategory integer NOT NULL,
    notifyreceivername     character varying(256),
    notifyaddress          character varying(510),
    notifycontent          character varying(510),
    notifyserverid         integer,
    eventfilterdelay       integer,
    eventfiltercount       integer,
    eventfilterid          integer,
    eventfilterconditionid integer,
    notifyservercategory   integer
);
ALTER TABLE ONLY eventnotifyreciever ADD CONSTRAINT idx_21982_primary PRIMARY KEY (stationid, equipmentid, eventid, eventseverity, eventstate, notifyreceiverid, notifyreceivercategory);

CREATE TABLE tbl_bizbasetypeid
(
    businesstypeid    integer NOT NULL,
    basetypeid        integer NOT NULL,
    storeinterval     double precision,
    absvaluethreshold double precision
);
ALTER TABLE ONLY tbl_bizbasetypeid ADD CONSTRAINT idx_22733_primary PRIMARY KEY (basetypeid, businesstypeid);

CREATE TABLE tbl_bizexpequsignalsmap
(
    businesstypeid    integer NOT NULL,
    expressionid      integer NOT NULL,
    associationid     integer NOT NULL,
    stationid         integer NOT NULL,
    monitorunitid     integer NOT NULL,
    serialid          integer NOT NULL,
    equipmentid       integer NOT NULL,
    signalid          integer NOT NULL,
    storeinterval     double precision,
    absvaluethreshold double precision
);
ALTER TABLE ONLY tbl_bizexpequsignalsmap ADD CONSTRAINT idx_22736_primary PRIMARY KEY (businesstypeid, equipmentid, expressionid, monitorunitid, serialid, signalid, stationid);

CREATE TABLE tbl_bizexpsignalscfg
(
    businesstypeid    integer NOT NULL,
    expressionid      integer NOT NULL,
    associationid     integer NOT NULL,
    stationid         integer NOT NULL,
    equipmentid       integer NOT NULL,
    signalid          integer NOT NULL,
    monitorunitid     integer NOT NULL,
    storeinterval     double precision,
    absvaluethreshold double precision
);
ALTER TABLE ONLY tbl_bizexpsignalscfg ADD CONSTRAINT idx_22739_primary PRIMARY KEY (businesstypeid, equipmentid, expressionid, signalid, stationid);

CREATE TABLE tbl_bizexpstationsmap
(
    businesstypeid         integer NOT NULL,
    expressionid           integer NOT NULL,
    stationid              integer NOT NULL,
    monitorunitid          integer NOT NULL,
    serialid               integer NOT NULL,
    expression             text,
    suppressexpression     text,
    statetriggervalue      integer DEFAULT 1,
    beforechgstoreinterval double precision,
    afterchgstoreinterval  double precision,
    errorflag              integer,
    note                   text
);
ALTER TABLE ONLY tbl_bizexpstationsmap ADD CONSTRAINT idx_22742_primary PRIMARY KEY (businesstypeid, expressionid, monitorunitid, serialid, stationid);

CREATE TABLE tbl_businessdataitem
(
    businessid    integer DEFAULT 0      NOT NULL,
    parententryid integer DEFAULT 0      NOT NULL,
    parentitemid  integer DEFAULT 0      NOT NULL,
    entryid       integer                NOT NULL,
    itemid        integer                NOT NULL,
    itemvalue     character varying(256) NOT NULL,
    itemalias     character varying(510),
    issystem      integer                NOT NULL,
    isdefault     integer                NOT NULL,
    enable        integer                NOT NULL,
    description   character varying(510),
    extendfield1  character varying(510),
    extendfield2  character varying(510),
    extendfield3  character varying(510),
    extendfield4  character varying(510),
    extendfield5  character varying(510)
);
ALTER TABLE ONLY tbl_businessdataitem ADD CONSTRAINT idx_22748_primary PRIMARY KEY (businessid, entryid, itemid);

CREATE TABLE tbl_businessexpressioncfg
(
    businesstypeid         integer NOT NULL,
    expressionid           integer NOT NULL,
    stationid              integer NOT NULL,
    monitorunitid          integer NOT NULL,
    expressionname         character varying(510),
    expression             text,
    suppressexpression     text,
    statetriggervalue      integer DEFAULT 1,
    beforechgstoreinterval double precision,
    afterchgstoreinterval  double precision,
    note                   text
);
ALTER TABLE ONLY tbl_businessexpressioncfg ADD CONSTRAINT idx_22756_primary PRIMARY KEY (businesstypeid, expressionid);

CREATE TABLE tbl_businesstype
(
    businesstypeid   integer                NOT NULL PRIMARY KEY,
    businesstypename character varying(510) NOT NULL,
    middletablename  character varying(510),
    note             text
);

CREATE TABLE tbl_businesstypestatus
(
    stationid      integer NOT NULL,
    businesstypeid integer NOT NULL,
    expressionid   integer NOT NULL,
    serialid       integer NOT NULL,
    businessstate  integer NOT NULL,
    starttime      timestamp NOT NULL,
    endtime        timestamp,
    groupid        integer
);
ALTER TABLE ONLY tbl_businesstypestatus ADD CONSTRAINT idx_22767_primary PRIMARY KEY (businessstate, businesstypeid, expressionid, serialid, starttime, stationid);

CREATE TABLE tbl_categoryidmap
(
    businessid         integer NOT NULL,
    categorytypeid     integer NOT NULL,
    originalcategoryid integer NOT NULL,
    businesscategoryid integer NOT NULL
);
ALTER TABLE ONLY tbl_categoryidmap ADD CONSTRAINT idx_22779_primary PRIMARY KEY (businesscategoryid, businessid, categorytypeid, originalcategoryid);

CREATE TABLE tbl_custominfo
(
    custominfoid  integer                NOT NULL PRIMARY KEY,
    userid        integer                NOT NULL,
    customtype    character varying(256) NOT NULL,
    customcontent text                   NOT NULL,
    createtime    timestamp NOT NULL,
    description   character varying(510)
);

CREATE TABLE tbl_experience
(
    experienceid      serial NOT NULL PRIMARY KEY,
    experiencecaption text,
    measure           text,
    description       text,
    lastupdatedate    timestamp DEFAULT localtimestamp NOT NULL,
    condition         text
);

CREATE TABLE tbl_expert
(
    expertid                  integer NOT NULL PRIMARY KEY,
    stationcategoryid         integer,
    stationcategoryname       character varying(510),
    baseequipmenttypeid       integer,
    baseequipmenttypename     character varying(510),
    basealarmid               integer NOT NULL,
    basealarmname             character varying(510),
    standardequipmenttypeid   integer,
    standardequipmenttypename character varying(510),
    standardalarmid           character varying(510),
    standardalarmname         character varying(510),
    reason                    text,
    solution                  text
);

CREATE TABLE tbl_fault
(
    faultid          integer                NOT NULL PRIMARY KEY,
    sequenceid       character varying(510) NOT NULL,
    uuid             character varying(256) NOT NULL,
    stationgroupname character varying(510),
    stationname      character varying(510),
    housename        character varying(510),
    equipmentname    character varying(510),
    eventname        character varying(510),
    starttime        timestamp NOT NULL,
    endtime          timestamp,
    createuserid     integer                NOT NULL,
    createtime       timestamp NOT NULL,
    casereason       text,
    casesolution     text
);

CREATE TABLE tbl_faultexpertmap
(
    faultexpertmapid serial  NOT NULL PRIMARY KEY,
    faultid          integer NOT NULL,
    expertid         integer NOT NULL
);

CREATE TABLE tbl_graphicpage
(
    id               integer                NOT NULL PRIMARY KEY,
    type             character varying(510),
    groupid          integer,
    pagecategory     integer,
    baseequipmentid  integer,
    objectid         bigint,
    globalresourceid bigint,
    name             character varying(510) NOT NULL,
    appendname       character varying(510),
    templateid       integer,
    data             text,
    style            text,
    children         text,
    description      character varying(510),
    routertype       character varying(510),
    crumbsroutertype character varying(510)
);

CREATE TABLE tbl_graphicpagetemplate
(
    id               integer                NOT NULL PRIMARY KEY,
    type             character varying(510),
    groupid          integer,
    pagecategory     integer,
    baseequipmentid  integer,
    internal         smallint,
    name             character varying(510) NOT NULL,
    appendname       character varying(510),
    data             text,
    style            text,
    children         text,
    description      character varying(510),
    templatecategory integer
);

CREATE TABLE tbl_homepage
(
    pageid      integer                NOT NULL PRIMARY KEY,
    pagename    character varying(510) NOT NULL,
    filename    character varying(510) NOT NULL,
    pagetype    integer                NOT NULL,
    description character varying(510)
);

CREATE TABLE tbl_userhomepagemap
(
    pageid integer NOT NULL,
    userid integer NOT NULL
);
ALTER TABLE ONLY tbl_userhomepagemap ADD CONSTRAINT idx_24168_primary PRIMARY KEY (pageid, userid);

CREATE TABLE tbl_kpipage
(
    kpipageid   integer                NOT NULL PRIMARY KEY,
    userid      integer                NOT NULL,
    name        character varying(256) NOT NULL,
    type        integer                NOT NULL,
    filename    character varying(256) NOT NULL,
    filedir     character varying(300),
    createtime  timestamp NOT NULL,
    modifytime  timestamp,
    description character varying(400),
    thumbimage  character varying(400)
);

CREATE TABLE tbl_kpipageuserrelate
(
    userid    integer NOT NULL PRIMARY KEY,
    kpipageid integer NOT NULL
);

CREATE TABLE tbl_equipmentbranch
(
    id            serial  NOT NULL PRIMARY KEY,
    branchid      integer NOT NULL,
    branchname    character varying(256),
    equipmentid   integer NOT NULL,
    equipmentname character varying(256)
);
CREATE INDEX idx_22986_idx_equipmentbranch_equipmentid_branchid ON tbl_equipmentbranch USING BTREE (equipmentid, branchid);

CREATE TABLE tbl_phoenixdiskfile
(
    fileid bigserial NOT NULL PRIMARY KEY,
    filepath   character varying(2048) NOT NULL,
    filename   character varying(512)  NOT NULL,
    status     integer,
    createtime timestamp
);
