CREATE TABLE alarmvideolink
(
    alarmvideolinkid serial NOT NULL PRIMARY KEY,
    configname       character varying(510),
    description      character varying(510),
    usedstatus       integer,
    departmentid     integer,
    linktype         character varying(32),
    operationtype    character varying(32),
    snapshotcount    integer,
    snapshotinterval integer
);
COMMENT ON COLUMN alarmvideolink.configname IS '实例名称';
COMMENT ON COLUMN alarmvideolink.description IS '实例描述';
COMMENT ON COLUMN alarmvideolink.usedstatus IS '启用状态';
COMMENT ON COLUMN alarmvideolink.departmentid IS '部门id';
COMMENT ON COLUMN alarmvideolink.linktype IS '联动类型';
COMMENT ON COLUMN alarmvideolink.operationtype IS '事件状态';
COMMENT ON COLUMN alarmvideolink.snapshotcount IS '抓图张数';
COMMENT ON COLUMN alarmvideolink.snapshotinterval IS '抓图间隔（秒）';

CREATE TABLE alarmvideolinkevent
(
    alarmvideolinkeventid serial NOT NULL PRIMARY KEY,
    alarmvideolinkmapid   integer,
    eventid               integer,
    eventconditionid      integer
);
COMMENT ON COLUMN alarmvideolinkevent.alarmvideolinkmapid IS '告警视频联动映射关系id';
COMMENT ON COLUMN alarmvideolinkevent.eventid IS '事件id';
COMMENT ON COLUMN alarmvideolinkevent.eventconditionid IS '事件条件id';

CREATE TABLE alarmvideolinkmap
(
    alarmvideolinkmapid serial NOT NULL PRIMARY KEY,
    alarmvideolinkid    integer,
    equipmentid         integer,
    eventid             integer,
    eventconditionid    integer,
    cameraid            bigint,
    cameraids           character varying(510)
);
COMMENT ON COLUMN alarmvideolinkmap.alarmvideolinkid IS '告警视频联动id';
COMMENT ON COLUMN alarmvideolinkmap.equipmentid IS '设备id';
COMMENT ON COLUMN alarmvideolinkmap.eventid IS '告警id';
COMMENT ON COLUMN alarmvideolinkmap.eventconditionid IS '告警条件id';
COMMENT ON COLUMN alarmvideolinkmap.cameraid IS '摄像头id';
COMMENT ON COLUMN alarmvideolinkmap.cameraids IS '摄像头ids，多个逗号隔开';
CREATE INDEX idx_21509_idx_equipmentid_eventid_eventconditionid ON alarmvideolinkmap USING BTREE (equipmentid, eventid, eventconditionid);

CREATE TABLE alarmchangesnapshot
(
    sequenceid    character varying(256) NOT NULL PRIMARY KEY,
    operationtype integer                NOT NULL,
    snapshoturl   character varying(256) NOT NULL,
    cameraid      integer                NOT NULL,
    cameraname    character varying(256),
    starttime     timestamp NOT NULL
);
COMMENT ON COLUMN alarmchangesnapshot.sequenceid IS '主键id';
COMMENT ON COLUMN alarmchangesnapshot.operationtype IS '告警状态';
COMMENT ON COLUMN alarmchangesnapshot.snapshoturl IS '抓图保存地址';
COMMENT ON COLUMN alarmchangesnapshot.cameraid IS '摄像头id';
COMMENT ON COLUMN alarmchangesnapshot.cameraname IS '摄像头名';
COMMENT ON COLUMN alarmchangesnapshot.starttime IS '告警开始时间';