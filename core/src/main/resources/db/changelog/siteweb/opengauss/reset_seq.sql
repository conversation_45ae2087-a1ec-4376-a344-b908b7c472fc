-- 重置最大自增序列
DO $$
    DECLARE
    rec RECORD;
max_id BIGINT;
BEGIN
    -- 查询所有自增列和对应的序列
    FOR rec IN
SELECT
    c.table_schema,
    c.table_name,
    c.column_name,
    substring(c.column_default FROM 'nextval\(''([^'']+)''') AS sequence_name
FROM
    information_schema.columns c
WHERE
    c.column_default LIKE 'nextval%'
    LOOP
-- 动态查询每个表的最大 ID
EXECUTE format('SELECT MAX(%I) FROM %I.%I', rec.column_name, rec.table_schema, rec.table_name) INTO max_id;

-- 如果表不为空，设置序列的当前值
IF max_id IS NOT NULL THEN
            -- 设置序列的当前值为最大 ID 值 +100是因为部分表有初始化数据是负数，+100以后会转会从正数，正数才能继续自增
EXECUTE format('SELECT setval(%L, %s)', rec.sequence_name, max_id + 100);
END IF;
END LOOP;
END $$;

-- ALTER SEQUENCE permissioncategory_permissioncategoryid_seq RESTART WITH 1000;
-- ALTER SEQUENCE permission_permissionid_seq RESTART WITH 1000;