CREATE TABLE safemessage
(
    safemessageid       serial NOT NULL PRIMARY KEY,
    configname          character varying(256),
    receiver            character varying(510),
    receivemode         character varying(256),
    sendtime            time WITHOUT TIME zone,
    sendtype            integer,
    sendtypedescription character varying(200),
    cron                character varying(200),
    contenttemplate     character varying(510),
    description         character varying(510),
    usedstatus          integer
);
COMMENT ON COLUMN safemessage.configname IS '平安短信配置名称';
COMMENT ON COLUMN safemessage.receiver IS '接收人 多个用逗号隔开';
COMMENT ON COLUMN safemessage.receivemode IS '接收方式 1短信  2语音 3.邮件 多个用逗号隔开';
COMMENT ON COLUMN safemessage.sendtime IS '发送的具体时间';
COMMENT ON COLUMN safemessage.sendtype IS '发送时间类型 1每天 2每周 3每月';
COMMENT ON COLUMN safemessage.sendtypedescription IS '每周的星期几与每月的多少号，多个用逗号隔开';
COMMENT ON COLUMN safemessage.cron IS 'corn表达式用于定时发送平安短信';
COMMENT ON COLUMN safemessage.contenttemplate IS '模板内容';
COMMENT ON COLUMN safemessage.description IS '配置描述';
COMMENT ON COLUMN safemessage.usedstatus IS '启用状态';

CREATE TABLE safemessageelementconfig
(
    safemessageelementconfigid serial NOT NULL PRIMARY KEY,
    safemessageid              integer,
    elementtype                integer,
    elementsetting             text
);
COMMENT ON COLUMN safemessageelementconfig.safemessageid IS '平安短信配置id';
COMMENT ON COLUMN safemessageelementconfig.elementtype IS '元素类型 1信号 2指标';
COMMENT ON COLUMN safemessageelementconfig.elementsetting IS '元素配置';

CREATE TABLE safemessagerecord
(
    safemessagerecordid serial NOT NULL PRIMARY KEY,
    safemessageid       integer,
    sendcontent         character varying(510),
    receiver            character varying(510),
    sendtime            timestamp,
    remark              character varying(256)
);
COMMENT ON COLUMN safemessagerecord.safemessageid IS '平安短信配置id';
COMMENT ON COLUMN safemessagerecord.sendcontent IS '发送内容';
COMMENT ON COLUMN safemessagerecord.receiver IS '接收人';
COMMENT ON COLUMN safemessagerecord.sendtime IS '发送时间';
COMMENT ON COLUMN safemessagerecord.remark IS '备注';