CREATE TABLE gene_generatorelecunitprice
(
    serialid          serial                NOT NULL PRIMARY KEY,
    equipmentid       integer               NOT NULL,
    equipmentbasetype integer,
    year              integer               NOT NULL,
    month             integer               NOT NULL,
    yearmonth         character varying(32) NOT NULL,
    elecunitprice     double precision      NOT NULL,
    updaterid         integer,
    updatetime        timestamp,
    extend1           character varying(510)
);
CREATE UNIQUE INDEX idx_22000_idx_gene_generatorelecunitprice_1 ON gene_generatorelecunitprice USING BTREE (equipmentid, YEAR, MONTH);

CREATE TABLE gene_generatorext
(
    geneid                integer          NOT NULL PRIMARY KEY,
    ratedpower            double precision,
    ratedpowerconsumption double precision,
    unitfuelconsumption   double precision,
    defaultelecunitprice  double precision NOT NULL,
    updaterid             integer,
    updatetime            timestamp,
    extend1               character varying(510)
);

CREATE TABLE gene_generatorhistorysignal
(
    serialid          serial                 NOT NULL PRIMARY KEY,
    geneid            integer                NOT NULL,
    signalid          integer                NOT NULL,
    geneidandsignalid character varying(510) NOT NULL,
    signalvalue       double precision,
    inserttime        timestamp NOT NULL,
    signalvalid       integer                NOT NULL,
    powerserialid     integer,
    makealarm         smallint,
    extend1           character varying(510)
);
CREATE INDEX idx_22008_idx_gene_historysignal_2 ON gene_generatorhistorysignal USING BTREE (geneid);
CREATE INDEX idx_22008_idx_gene_historysignal_1 ON gene_generatorhistorysignal USING BTREE (geneidandsignalid);

CREATE TABLE gene_generatorsignalcheckinfo
(
    serialid            serial                 NOT NULL PRIMARY KEY,
    geneid              integer                NOT NULL,
    equipmenttemplateid integer                NOT NULL,
    signalid            integer                NOT NULL,
    signalname          character varying(510) NOT NULL,
    dynamicorstatic     integer                NOT NULL,
    triggervalue        double precision,
    extend1             character varying(510),
    extend2             character varying(510)
);
CREATE INDEX idx_22015_idx_gene_checksignal_1 ON gene_generatorsignalcheckinfo USING BTREE (geneid);

CREATE TABLE gene_maintenancerecord
(
    serialid        serial  NOT NULL PRIMARY KEY,
    geneid          integer NOT NULL,
    genename        character varying(512),
    maintenancetime timestamp NOT NULL,
    "position"      character varying(2048),
    maintenanceitem character varying(2048),
    maintainer      character varying(256),
    confirmor       character varying(256),
    remark          character varying(2048),
    updaterid       integer,
    updatetime      timestamp,
    extend1         character varying(510)
);
CREATE INDEX idx_22022_idx_gene_maintenancerecord_1 ON gene_maintenancerecord USING BTREE (geneid);

CREATE TABLE gene_oilboxext
(
    oilid        integer NOT NULL PRIMARY KEY,
    ratedvolume  double precision,
    fulloillevel double precision,
    updaterid    integer,
    updatetime   timestamp,
    extend1      character varying(510)
);

CREATE TABLE gene_oilboxgeneratormap
(
    oilid      integer NOT NULL,
    geneid     integer NOT NULL,
    updaterid  integer,
    updatetime timestamp,
    extend1    character varying(510)
);
ALTER TABLE ONLY gene_oilboxgeneratormap ADD CONSTRAINT idx_22031_primary PRIMARY KEY (oilid, geneid);

CREATE TABLE gene_oillevelrecord
(
    serialid        serial           NOT NULL PRIMARY KEY,
    oilid           integer          NOT NULL,
    currentoillevel double precision NOT NULL,
    updatetime      timestamp NOT NULL,
    updateuserid    integer          NOT NULL,
    extend1         character varying(510)
);
CREATE INDEX idx_22035_idx_gene_oillevel_1 ON gene_oillevelrecord USING BTREE (oilid);

CREATE TABLE gene_powergenerationrecord
(
    serialid                serial           NOT NULL PRIMARY KEY,
    equipmentid             integer          NOT NULL,
    equipmentbasetype       integer,
    starttime               timestamp NOT NULL,
    endtime                 timestamp,
    positiveelecenergystart double precision NOT NULL,
    positiveelecenergyend   double precision,
    powergeneration         double precision,
    runduration             bigint,
    powergenerationcost     double precision,
    powergenerationoil      double precision,
    powergenerationoilcost  double precision,
    startinserttime         timestamp,
    endinserttime           timestamp,
    startserialno           bigint,
    endserialno             bigint,
    extend1                 character varying(510)
);
CREATE INDEX idx_22040_idx_powergenerationrecord_3fields_1 ON gene_powergenerationrecord USING BTREE (equipmentid, equipmentbasetype, starttime);

CREATE TABLE gene_refuelrecord
(
    serialid       serial           NOT NULL PRIMARY KEY,
    oilid          integer          NOT NULL,
    oilname        character varying(510),
    refueltime     timestamp NOT NULL,
    refuelquantity double precision NOT NULL,
    unitprice      double precision,
    refuelfee      double precision,
    operator       character varying(510),
    updaterid      integer,
    updatetime     timestamp,
    extend1        character varying(510)
);
CREATE INDEX idx_22045_idx_gene_refuelrecord_1 ON gene_refuelrecord USING BTREE (oilid);