CREATE TABLE tbl_activecontrolofdoor
(
    stationid       integer NOT NULL,
    hostid          integer NOT NULL,
    equipmentid     integer NOT NULL,
    controlid       integer NOT NULL,
    userid          integer NOT NULL,
    parametervalues text,
    description     character varying(510),
    lastupdate      timestamp NOT NULL,
    id              serial  NOT NULL PRIMARY KEY
);
CREATE INDEX idx_22626_idx_activedoorcontrol_hostid ON tbl_activecontrolofdoor USING BTREE (hostid);
CREATE INDEX idx_22626_idx_activedoorcontrol_complex ON tbl_activecontrolofdoor USING BTREE (stationid, equipmentid, controlid, userid);

CREATE TABLE tbl_activecontrolofdoor_fail
(
    stationid       integer NOT NULL,
    hostid          integer NOT NULL,
    equipmentid     integer NOT NULL,
    controlid       integer NOT NULL,
    userid          integer NOT NULL,
    parametervalues text,
    description     character varying(510),
    lastupdate      timestamp NOT NULL,
    sendresult      integer
);
CREATE INDEX idx_22632_idx_tbl_activecontrolofdoor_fail_hostid ON tbl_activecontrolofdoor_fail USING BTREE (hostid);
CREATE INDEX idx_22632_idx_tbl_activecontrolofdoor_fail_complex ON tbl_activecontrolofdoor_fail USING BTREE (stationid, equipmentid, controlid, userid);

CREATE TABLE tbl_card
(
    cardid         integer               NOT NULL PRIMARY KEY,
    cardcode       character varying(40) NOT NULL,
    cardname       character varying(256),
    cardcategory   integer,
    cardgroup      integer,
    userid         integer,
    stationid      integer,
    cardstatus     integer,
    starttime      timestamp,
    endtime        timestamp,
    registertime   timestamp,
    unregistertime timestamp,
    losttime       timestamp,
    description    character varying(510)
);

CREATE TABLE tbl_cardext
(
    cardid       integer NOT NULL PRIMARY KEY,
    password     character varying(60),
    cardcodetype integer,
    cardcodeconv character varying(40)
);
COMMENT ON COLUMN tbl_cardext.cardcodetype IS '卡号类型';
COMMENT ON COLUMN tbl_cardext.cardcodeconv IS '卡号转换(tbl_card.cardcode的十进制或十六进制)';

CREATE TABLE tbl_cardtypemap
(
    cardid   integer NOT NULL,
    cardtype integer NOT NULL
);

CREATE TABLE tbl_ddscardno
(
    doorid integer,
    cardid integer,
    cardno integer
);

CREATE TABLE tbl_door
(
    doorid        integer NOT NULL PRIMARY KEY,
    doorno        integer NOT NULL,
    doorname      character varying(256),
    stationid     integer NOT NULL,
    equipmentid   integer NOT NULL,
    samplerunitid integer,
    category      integer NOT NULL,
    address       character varying(510),
    workmode      integer,
    infrared      integer,
    password      character varying(20),
    doorcontrolid integer,
    doorinterval  integer,
    opendelay     integer,
    description   character varying(510),
    openmode      integer
);
CREATE INDEX idx_22893_idx_door_equipment ON tbl_door USING BTREE (equipmentid, doorid);

CREATE TABLE tbl_doorarea
(
    areaid      integer NOT NULL PRIMARY KEY,
    areaname    character varying(510),
    parentid    integer,
    description character varying(510)
);
COMMENT ON COLUMN tbl_doorarea.areaid IS '门区域ID';
COMMENT ON COLUMN tbl_doorarea.areaname IS '门区域名称';
COMMENT ON COLUMN tbl_doorarea.parentid IS '父门区域ID';
COMMENT ON COLUMN tbl_doorarea.description IS '描述';

CREATE TABLE tbl_doorareamap
(
    areaid      integer NOT NULL,
    equipmentid integer NOT NULL PRIMARY KEY
);
COMMENT ON COLUMN tbl_doorareamap.areaid IS '门区域ID';
COMMENT ON COLUMN tbl_doorareamap.equipmentid IS '门设备ID';

CREATE TABLE tbl_doorcard
(
    cardid        integer NOT NULL,
    timegroupid   integer NOT NULL,
    doorid        integer NOT NULL,
    starttime     timestamp,
    endtime       timestamp,
    password      character varying(60),
    timegrouptype integer NOT NULL
);
ALTER TABLE ONLY tbl_doorcard ADD CONSTRAINT idx_22906_primary PRIMARY KEY (cardid, doorid, timegroupid, timegrouptype);

CREATE TABLE tbl_doorcardlost
(
    cardid      integer NOT NULL,
    timegroupid integer NOT NULL,
    doorid      integer NOT NULL,
    starttime   timestamp,
    endtime     timestamp,
    password    character varying(60)
);
ALTER TABLE ONLY tbl_doorcardlost ADD CONSTRAINT idx_22909_primary PRIMARY KEY (cardid, doorid, timegroupid);

CREATE TABLE tbl_doorcontroller
(
    doorcontrolid   integer                NOT NULL PRIMARY KEY,
    doorcontrolname character varying(256) NOT NULL,
    licensekey      character varying(60),
    display         integer,
    cardlength      integer                NOT NULL,
    maxdoorcount    integer                NOT NULL,
    dllpath         character varying(256),
    description     character varying(510)
);

CREATE TABLE tbl_doorgroup
(
    doorgroupid   integer                NOT NULL PRIMARY KEY,
    doorgroupname character varying(510) NOT NULL,
    description   character varying(510),
    lasttime      timestamp
);

CREATE TABLE tbl_doorgroupmap
(
    doorgroupid integer NOT NULL,
    equipmentid integer NOT NULL,
    lasttime    timestamp
);

CREATE TABLE tbl_doorparam
(
    doorid     integer NOT NULL,
    paramtype  integer NOT NULL,
    paramvalue integer
);
COMMENT ON COLUMN tbl_doorparam.doorid IS '门ID';
COMMENT ON COLUMN tbl_doorparam.paramtype IS '参数类型';
COMMENT ON COLUMN tbl_doorparam.paramvalue IS '门参数值';
ALTER TABLE ONLY tbl_doorparam ADD CONSTRAINT idx_22925_primary PRIMARY KEY (doorid, paramtype);

CREATE TABLE tbl_doorproperty
(
    category     integer NOT NULL,
    propertytype integer NOT NULL
);
COMMENT ON COLUMN tbl_doorproperty.category IS '门类型';
COMMENT ON COLUMN tbl_doorproperty.propertytype IS '属性类型';
CREATE UNIQUE INDEX idx_22928_category ON tbl_doorproperty USING BTREE (category, propertytype);

CREATE TABLE tbl_doorpropertymeaning
(
    category     integer NOT NULL,
    propertytype integer NOT NULL,
    propertyid   integer NOT NULL,
    meaning      character varying(510)
);
COMMENT ON COLUMN tbl_doorpropertymeaning.category IS '门类型';
COMMENT ON COLUMN tbl_doorpropertymeaning.propertytype IS '属性类型';
COMMENT ON COLUMN tbl_doorpropertymeaning.propertyid IS '属性ID';
COMMENT ON COLUMN tbl_doorpropertymeaning.meaning IS '属性含义';
CREATE UNIQUE INDEX idx_22931_category ON tbl_doorpropertymeaning USING BTREE (category, propertytype, propertyid);

CREATE TABLE tbl_doortimegroup
(
    doorid        integer NOT NULL,
    timegroupid   integer NOT NULL,
    timegrouptype integer NOT NULL
);
ALTER TABLE ONLY tbl_doortimegroup ADD CONSTRAINT idx_22934_primary PRIMARY KEY (doorid, timegroupid, timegrouptype);

CREATE TABLE tbl_facedata
(
    faceid integer NOT NULL PRIMARY KEY,
    facedata bytea NOT NULL
);

CREATE TABLE tbl_facedataauth
(
    timegroupid    integer NOT NULL,
    doorid         integer NOT NULL,
    cardid         integer NOT NULL,
    faceid         integer NOT NULL,
    lastupdatetime timestamp DEFAULT localtimestamp
);
ALTER TABLE ONLY tbl_facedataauth ADD CONSTRAINT idx_23161_primary PRIMARY KEY (cardid, doorid, faceid, timegroupid);

CREATE TABLE tbl_facedatamap
(
    cardid         integer NOT NULL,
    faceid         integer NOT NULL,
    lastupdater    integer NOT NULL,
    lastupdatetime timestamp DEFAULT localtimestamp NOT NULL
);
ALTER TABLE ONLY tbl_facedatamap ADD CONSTRAINT idx_23165_primary PRIMARY KEY (cardid, faceid);

CREATE TABLE tbl_fingerprint
(
    fingerprintid integer NOT NULL,
    fingerprintno integer NOT NULL,
    fingerprintdata bytea NOT NULL
);
ALTER TABLE ONLY tbl_fingerprint ADD CONSTRAINT idx_23179_primary PRIMARY KEY (fingerprintid, fingerprintno);

CREATE TABLE tbl_fingerprintauth
(
    timegroupid    integer NOT NULL,
    doorid         integer NOT NULL,
    cardid         integer NOT NULL,
    fingerprintid  integer NOT NULL,
    lastupdatetime timestamp NOT NULL
);
ALTER TABLE ONLY tbl_fingerprintauth ADD CONSTRAINT idx_23184_primary PRIMARY KEY (cardid, doorid, fingerprintid, timegroupid);

CREATE TABLE tbl_fingerprintcardmap
(
    cardid         integer NOT NULL,
    fingerprintid  integer NOT NULL,
    vendor         integer,
    lastupdater    integer NOT NULL,
    lastupdatetime timestamp NOT NULL
);
ALTER TABLE ONLY tbl_fingerprintcardmap ADD CONSTRAINT idx_23187_primary PRIMARY KEY (cardid, fingerprintid);

CREATE TABLE tbl_fingerprintdatadistributelogrecord
(
    cardid              integer           NOT NULL,
    readerequipid       integer           NOT NULL,
    log                 text,
    isdistributesucceed integer DEFAULT 0 NOT NULL
);
ALTER TABLE ONLY tbl_fingerprintdatadistributelogrecord ADD CONSTRAINT idx_23190_primary PRIMARY KEY (cardid, readerequipid);

CREATE TABLE tbl_fingerprintdatamap
(
    cardid              integer           NOT NULL,
    fingeruserid        character varying(80),
    fingerprintno       integer           NOT NULL,
    password            character varying(40),
    fingerprintdata1 bytea NOT NULL,
    fingerprintdata2 bytea,
    fingerprintdata3 bytea,
    fingerdatasize1     integer           NOT NULL,
    fingerdatasize2     integer           NOT NULL,
    fingerdatasize3     integer           NOT NULL,
    isdistributesucceed integer DEFAULT 0 NOT NULL
);
ALTER TABLE ONLY tbl_fingerprintdatamap ADD CONSTRAINT idx_23196_primary PRIMARY KEY (cardid, fingerprintno);

CREATE TABLE tbl_fingerprintmap
(
    cardid        integer NOT NULL,
    fingeruserid  integer NOT NULL,
    fingerprintno integer NOT NULL,
    fingerprintdata bytea NOT NULL,
    fingerstatus  integer NOT NULL
);

CREATE TABLE tbl_fingerreadermap
(
    readerid             integer                NOT NULL PRIMARY KEY,
    readername           character varying(256) NOT NULL,
    readertype           integer                NOT NULL,
    readercommtype       integer                NOT NULL,
    readercommaddress    character varying(510) NOT NULL,
    samplertype          integer                NOT NULL,
    portid               integer                NOT NULL,
    netid                integer,
    doorid               integer,
    multidooropentype    integer,
    doorinoutflag        integer,
    isdistributesucceed  integer DEFAULT 0,
    isclearconfigsucceed integer,
    note                 character varying(510)
);

CREATE TABLE tbl_swapcardrecord
(
    stationid        integer,
    stationname      character varying(510),
    equipmentid      integer,
    equipmentname    character varying(256),
    cardstationid    integer,
    cardstationname  character varying(510),
    cardid           integer,
    cardcode         character varying(510),
    cardname         character varying(510),
    carduserid       integer,
    cardusername     character varying(510),
    cardcategory     integer,
    cardcategoryname character varying(510),
    cardgroup        integer,
    cardgroupname    character varying(510),
    cardstatus       integer,
    cardstatusname   character varying(510),
    doorid           integer,
    doorno           integer,
    doorname         character varying(510),
    doorcategory     integer,
    doorcategoryname character varying(510),
    valid            integer,
    validname        character varying(256),
    enter            smallint,
    recordtime       timestamp
);
CREATE UNIQUE INDEX idx_24116_tbl_swapcardrecord_idx1 ON tbl_swapcardrecord USING BTREE (recordtime, cardid, doorno);

CREATE TABLE tbl_swapcardrecordmid
(
    stationid        integer,
    stationname      character varying(510),
    equipmentid      integer,
    equipmentname    character varying(256),
    cardstationid    integer,
    cardstationname  character varying(510),
    cardid           integer,
    cardcode         character varying(510),
    cardname         character varying(510),
    carduserid       integer,
    cardusername     character varying(510),
    cardcategory     integer,
    cardcategoryname character varying(510),
    cardgroup        integer,
    cardgroupname    character varying(510),
    cardstatus       integer,
    cardstatusname   character varying(510),
    doorid           integer,
    doorno           integer,
    doorname         character varying(510),
    doorcategory     integer,
    doorcategoryname character varying(510),
    valid            integer,
    validname        character varying(256),
    enter            smallint,
    recordtime       timestamp
);

CREATE TABLE doortag
(
    tagid       integer                NOT NULL PRIMARY KEY,
    tagname     character varying(510) NOT NULL,
    tagicon     character varying(510),
    tagcolor    character varying(510),
    tagdescribe character varying(510)
);
COMMENT ON COLUMN doortag.tagid IS '设置主键自增';
COMMENT ON COLUMN doortag.tagname IS '标签名称';
COMMENT ON COLUMN doortag.tagicon IS '标签图标';
COMMENT ON COLUMN doortag.tagcolor IS '标签颜色';

CREATE TABLE doortagmap
(
    id          integer NOT NULL PRIMARY KEY,
    equipmentid integer,
    tagid       integer NOT NULL
);
COMMENT ON COLUMN doortagmap.id IS '设置主键自增';
COMMENT ON COLUMN doortagmap.tagid IS '标签主键id';

CREATE TABLE doorcardbackup
(
    doorcardbackupid integer NOT NULL PRIMARY KEY,
    type             integer NOT NULL,
    id               integer NOT NULL,
    deleteid         character varying(400),
    deletename       character varying(400),
    deletetime       character varying(510)
);
COMMENT ON COLUMN doorcardbackup.doorcardbackupid IS '设置主键自增';
COMMENT ON COLUMN doorcardbackup.type IS '类型 1设备 2卡';
COMMENT ON COLUMN doorcardbackup.id IS '设备或者卡的id';
COMMENT ON COLUMN doorcardbackup.deletetime IS '被删除的时间';