CREATE TABLE prealarm
(
    prealarmid           serial  NOT NULL PRIMARY KEY,
    prealarmpointid      integer NOT NULL,
    meanings             character varying(256),
    prealarmseverity     integer,
    prealarmseverityname character varying(256),
    prealarmcategory     integer,
    prealarmcategoryname character varying(256),
    color                character varying(256),
    uniqueid             character varying(512),
    uniquename           character varying(512),
    objectid             integer,
    objecttypeid         integer,
    objectname           character varying(256),
    resourcestructureid  integer,
    levelofpath          character varying(512),
    levelofpathname      character varying(512),
    triggervalue         character varying(256),
    unit                 character varying(20),
    sampletime           timestamp,
    starttime            timestamp,
    endtime              timestamp,
    confirmtime          timestamp,
    confirmid            integer,
    confirmname          character varying(256),
    remark               character varying(510),
    businesstypeid       integer
);

CREATE TABLE prealarmcategory
(
    categoryid       serial                NOT NULL PRIMARY KEY,
    categoryname     character varying(128) NOT NULL,
    parentcategoryid integer,
    description      character varying(256)
);

CREATE TABLE prealarmhistory
(
    prealarmid           integer NOT NULL,
    prealarmpointid      integer NOT NULL,
    meanings             character varying(256),
    prealarmseverity     integer,
    prealarmseverityname character varying(256),
    prealarmcategory     integer,
    prealarmcategoryname character varying(256),
    color                character varying(256),
    uniqueid             character varying(512),
    uniquename           character varying(512),
    objectid             integer,
    objecttypeid         integer,
    objectname           character varying(256),
    resourcestructureid  integer,
    levelofpath          character varying(512),
    levelofpathname      character varying(512),
    triggervalue         character varying(256),
    unit                 character varying(20),
    sampletime           timestamp,
    starttime            timestamp,
    endtime              timestamp,
    confirmtime          timestamp,
    confirmid            integer,
    confirmname          character varying(256),
    remark               character varying(510),
    businesstypeid       integer
);

CREATE TABLE prealarmpoint
(
    prealarmpointid     serial NOT NULL PRIMARY KEY,
    prealarmpointname   character varying(256),
    meanings            character varying(256),
    expression          character varying(512),
    abnormalexpression  character varying(512),
    executecron         character varying(512),
    prealarmcategory    integer,
    uniqueid            character varying(512),
    objectid            integer,
    objecttypeid        integer,
    objectname          character varying(256),
    resourcestructureid integer,
    levelofpath         character varying(512),
    levelofpathname     character varying(512),
    prealarmseverity    integer,
    enable              integer,
    unit                character varying(20),
    masktype            integer,
    maskduration        character varying(256),
    maskstarttime       timestamp,
    maskendtime         timestamp,
    stateful            integer,
    modifier            integer,
    modifiername        character varying(256),
    modifytime          timestamp
);

CREATE TABLE prealarmseverity
(
    prealarmseverityid   serial                NOT NULL PRIMARY KEY,
    prealarmseverityname character varying(128),
    color                character varying(256),
    description          character varying(256)
);

CREATE TABLE prealarmchange
(
    sequenceid            character varying(256) NOT NULL,
    serialno bigserial NOT NULL PRIMARY KEY,
    operationtype         integer                NOT NULL,
    prealarmid            bigint                 NOT NULL,
    prealarmname          character varying(256) NOT NULL,
    meanings              character varying(510) NOT NULL,
    triggervalue          character varying(510) NOT NULL,
    prealarmpointid       bigint                 NOT NULL,
    prealarmcategory      integer                NOT NULL,
    prealarmcategoryname  character varying(256),
    prealarmseverity      integer                NOT NULL,
    prealarmseverityname  character varying(256),
    starttime             timestamp NOT NULL,
    endtime               timestamp,
    confirmtime           timestamp,
    confirmorid           integer,
    confirmorname         character varying(256),
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256) NOT NULL,
    equipmentcategory     integer                NOT NULL,
    equipmentcategoryname character varying(256) NOT NULL,
    equipmentvendor       character varying(256),
    centerid              integer                NOT NULL,
    centername            character varying(256),
    structureid           integer                NOT NULL,
    structurename         character varying(256),
    stationid             integer                NOT NULL,
    stationname           character varying(256),
    stationcategoryid     integer                NOT NULL,
    resourcestructureid   integer                NOT NULL,
    levelofpathname       character varying(510),
    inserttime            timestamp NOT NULL
);
CREATE INDEX idx_22337_tbl_alarmchange_id1 ON prealarmchange USING BTREE (sequenceid);