CREATE TABLE tbl_sparkindexbaseequipment
(
    groupid         integer NOT NULL,
    baseequipmentid integer NOT NULL,
    grapicpageid    integer,
    displayindex    integer,
    displayformat   text,
    extendfiled1    text,
    extendfiled2    text,
    displaytype     integer
);

CREATE TABLE tbl_sparkindexbasesignal
(
    groupid         integer        NOT NULL,
    baseequipmentid integer        NOT NULL,
    basetypeid      numeric(12, 0) NOT NULL,
    displayindex    integer,
    extendfiled1    text,
    extendfiled2    text
);

CREATE TABLE tbl_sparkindexgroup
(
    groupid      integer                NOT NULL,
    grouptitle   character varying(510) NOT NULL,
    grapicpageid integer,
    extendfiled1 text,
    extendfiled2 text,
    columncount  integer
);