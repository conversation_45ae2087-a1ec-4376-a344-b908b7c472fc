<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_RegionMap_info" author="habits" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="RegionMap" remarks="RegionMap info table">
            <column name="RegionMapId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ResourceStructureId" type="INT" remarks="资源组ID">
                <constraints nullable="false" primaryKey="true"
                             primaryKeyName="PK_ResourceStructureId_EquipmentId_AreaId"/>
            </column>
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false" primaryKey="true"
                             primaryKeyName="PK_ResourceStructureId_EquipmentId_AreaId"/>
            </column>
            <column name="RegionId" type="INT" remarks="区域ID">
                <constraints nullable="false" primaryKey="true"
                             primaryKeyName="PK_ResourceStructureId_EquipmentId_AreaId"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="RegionMap" columnName="RegionMapId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>