<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_alarmmasklog_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmMaskLog"
                     remarks="basic alarmmasklog info table">
            <column name="Id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UserId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperationType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperationTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="TimeGroupCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="TimeGroupChars" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Comment" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmMaskLog" columnName="Id" incrementBy="1" columnDataType="bigint"
                          startWith="1"/>
        <createIndex tableName="AlarmMaskLog" indexName="AlarmMaskLog_IDX1" unique="false">
            <column name="OperationTime"></column>
            <column name="EquipmentId"></column>
            <column name="EventId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>