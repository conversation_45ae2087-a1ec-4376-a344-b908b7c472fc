<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

     <changeSet id="create_tslSampler_info" author="vincent"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TSL_Sampler" remarks="tslSampler info table">
            <column name="SamplerId" type="int" remarks="采集器ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SamplerName" type="varchar(128)" remarks="采集器名称">
                <constraints nullable="false"/>
            </column>
            <column name="SamplerType" type="smallint" remarks="采集器类型">
                <constraints nullable="false"/>
            </column>
            <column name="ProtocolCode" type="varchar(32)" remarks="协议MD5码">
                <constraints nullable="false"/>
            </column>
            <column name="DLLCode" type="varchar(32)" remarks="采集库MD5码">
                <constraints nullable="false"/>
            </column>
            <column name="DLLVersion" type="varchar(32)" remarks="协议版本">
                <constraints nullable="false"/>
            </column>
            <column name="ProtocolFilePath" type="varchar(255)" remarks="协议文件路径">
                <constraints nullable="false"/>
            </column>
            <column name="DLLFilePath" type="varchar(255)" remarks="采集器路径">
                <constraints nullable="false"/>
            </column>
            <column name="DLLPath" type="varchar(255)" remarks="采集器路径">
                <constraints nullable="false"/>
            </column>
            <column name="Setting" type="varchar(255)"  remarks="采集设置（端口号波特率等）">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="SoCode" type="varchar(32)" remarks="so文件编码">
                <constraints nullable="false"/>
            </column>
            <column name="SoPath" type="varchar(255)" remarks="so文件路径">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TSL_Sampler" columnName="SamplerId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>