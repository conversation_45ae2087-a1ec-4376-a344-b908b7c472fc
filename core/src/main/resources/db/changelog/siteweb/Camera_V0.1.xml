<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="Camera" author="liaoximing" >
        <createTable tableName="Camera"
                     remarks="摄像头信息表">
            <column name="CameraId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CameraName" type="varchar(255)" remarks="摄像头名称">
                <constraints nullable="false" />
            </column>
            <column name="CameraIp" type="varchar(128)" remarks="摄像头ip">
                <constraints nullable="false" />
            </column>
            <column name="CameraPort" type="int" remarks="摄像头端口">
                <constraints nullable="false" />
            </column>
            <column name="ChannelNumber" type="varchar(32)" remarks="信道号">
                <constraints nullable="true"/>
            </column>
            <column name="CameraGroupId" type="int" remarks="摄像头分组id">
                <constraints nullable="true"/>
            </column>
            <column name="UserName" type="varchar(128)" remarks="用户名">
                <constraints nullable="true"/>
            </column>
            <column name="Password" type="varchar(32)" remarks="密码">
                <constraints nullable="true"/>
            </column>
            <column name="VendorId" type="int" remarks="厂商id">
                <constraints nullable="true"/>
            </column>
            <column name="VendorName" type="varchar(128)" remarks="厂商名称">
                <constraints nullable="true"/>
            </column>
            <column name="CameraIndexCode" type="varchar(255)" remarks="唯一id">
                <constraints nullable="true"/>
            </column>
            <column name="CameraType" type="int" remarks="摄像头类型">
                <constraints nullable="true"/>
            </column>
            <column name="CameraTypeName" type="varchar(255)" remarks="摄像头名称">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="最近更新时间">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Camera" columnName="CameraId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>