<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="LicenseFeature" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="LicenseFeature" remarks="模块表信息">
            <column name="FeatureId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Name" type="varchar(255)" remarks="模块名称">
                <constraints nullable="false" />
            </column>
            <column name="IsActive" type="tinyint" remarks="是否激活 0否 1是">
                <constraints nullable="false" />
            </column>
            <column name="Data" type="varchar(255)" remarks="附加扩展信息">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="LicenseFeature" columnName="FeatureId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>