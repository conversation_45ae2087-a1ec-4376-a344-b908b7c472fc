<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_accountalias_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AccountAlias"
                     remarks="basic AccountAlias info table">
            <column name="AccountAliasId" type="int" remarks="ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false" />
            </column>
            <column name="Alias" type="varchar(128)" remarks="别名">
                <constraints nullable="false"/>
            </column>
            <column name="Checked" type="tinyint(1)" remarks="是否默认">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AccountAlias" columnName="AccountAliasId" incrementBy="1"
                          columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>