<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_systemconfig_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="SystemConfig"
                     remarks="systemConfig info table">
            <column name="SystemConfigId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SystemConfigKey" type="varchar(255)" remarks="键名">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="SystemConfigValue" type="varchar(255)" remarks="对应键值">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="SystemConfigType" type="int" remarks="键值对业务类型">
                <constraints nullable="true" unique="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true" unique="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SystemConfig" columnName="SystemConfigId" incrementBy="1" columnDataType="int" startWith="1000"/>
    </changeSet>
</databaseChangeLog>