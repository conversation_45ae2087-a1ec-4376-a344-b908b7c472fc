<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="DoorCardBackup" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DoorCardBackup" remarks="门卡备份记录表 门删除时备份卡 卡删除时备份门">
            <column name="DoorCardBackupId" type="int" remarks="主键自增id">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="Type" type="int" remarks="被备份的类型 1设备(删卡) 2卡(删门)">
                <constraints nullable="false"/>
            </column>
            <column name="Id" type="int" remarks="被备份的id(被删除的设备或者卡的id)">
                <constraints nullable="false"/>
            </column>
            <column name="DeleteId" type="int" remarks="被删除的id">
                <constraints nullable="false"/>
            </column>
            <column name="DeleteName" type="int" remarks="被删除的名称">
                <constraints nullable="false"/>
            </column>
            <column name="DeleteTime" type="datetime" remarks="被删除的时间">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="DoorCardBackup" columnName="DoorCardBackupId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>