-- 门禁卡授权查询速度慢
CREATE INDEX idx_door_equipment ON TBL_Door (EquipmentId, DoorId);
CREATE INDEX idx_doorcard_door ON TBL_DoorCard (DoorId, CardId, TimeGroupId, TimeGroupType);
CREATE INDEX idx_doortimegroup_timegroup ON TBL_DoorTimeGroup (TimeGroupId, DoorId, TimeGroupType);

INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(1, '公安', 'icon-gongan', '#ADD8E6', '公安门禁');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(2, '一级', 'icon-yiji', '#ADD8E6', '一级门禁');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(3, 'IDC', 'icon-IDC', '#ADD8E6', 'IDC门禁');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(4, '国密', 'icon-guomi', '#ADD8E6', '国密门禁');

INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (5,'DDS门禁',NULL,10,8,4,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (17, 'CHD200D7门禁(子父设备合并)', NULL, 16, 10, 1, '', NULL);