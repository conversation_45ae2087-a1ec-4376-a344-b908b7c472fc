<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_chartstyle_info" author="Liu.Yandong.Hanks">
        <createTable tableName="ChartStyle" remarks="ChartStyle info table">
            <column name="StyleId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StyleName" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="ChartId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Thumbnail" type="text">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="text">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ChartStyle" columnName="StyleId" incrementBy="1" columnDataType="int" startWith="1"/>

        <createIndex tableName="ChartStyle" indexName="ChartStyle_StyleName" unique="true">
            <column name="StyleName"></column>
        </createIndex>
    </changeSet>

</databaseChangeLog>