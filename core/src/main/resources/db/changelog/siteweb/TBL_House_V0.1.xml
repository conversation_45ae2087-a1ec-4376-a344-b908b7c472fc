<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_TBLHouse_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_House" remarks="TBL_House info table">
            <column name="Id" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="HouseName" type="varchar(255)" remarks="局房名">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="int" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="HouseId" type="int" remarks="局房ID">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="LastUpdateDate" type="datetime" remarks="更新时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="TBL_House" indexName="IDX_House_Id" unique="true">
            <column name="StationId"></column>
            <column name="HouseId"></column>
        </createIndex>
        <addAutoIncrement tableName="TBL_House" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>