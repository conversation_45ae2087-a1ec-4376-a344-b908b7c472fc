<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_scene_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="Scene" remarks="scene info table">
            <column name="SceneId" type="int" remarks="场景唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneName" type="varchar(128)" remarks="场景名称">
                <constraints nullable="false"/>
            </column>
            <column name="Checked" type="tinyint(1)" remarks="是否已勾选">
                <constraints nullable="false"/>
            </column>
            <column name="Remark" type="varchar(128)" remarks="备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Scene" columnName="SceneId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>