<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_notification_info" author="lzy">
        <createTable tableName="Notification" remarks="消息通知">
            <column name="NotificationId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Pushed" type="tinyint(1)" remarks="推送是否成功">
                <constraints nullable="false"/>
            </column>
            <column name="Category" type="varchar(255)" remarks="通知分类">
                <constraints nullable="true"/>
            </column>
            <column name="Title" type="varchar(255)" remarks="通知标题">
                <constraints nullable="false"/>
            </column>
            <column name="Content" type="varchar(255)" remarks="通知内容">
                <constraints nullable="false"/>
            </column>
            <column name="Sender" type="varchar(255)" remarks="发送者">
                <constraints nullable="true"/>
            </column>
            <column name="Color" type="varchar(255)" remarks="颜色">
                <constraints nullable="true"/>
            </column>
            <column name="Icon" type="varchar(255)" remarks="图标">
                <constraints nullable="true"/>
            </column>
            <column name="WebLink" type="varchar(255)" remarks="web跳转链接">
                <constraints nullable="true"/>
            </column>
            <column name="AppLink" type="varchar(255)" remarks="app跳转链接">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
            <column name="ExternalId" type="varchar(50)" remarks="外部消息厂商请求标识">
                <constraints nullable="true"/>
            </column>
            <column name="ExtParam" type="text" remarks="扩展参数">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Notification" columnName="NotificationId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>