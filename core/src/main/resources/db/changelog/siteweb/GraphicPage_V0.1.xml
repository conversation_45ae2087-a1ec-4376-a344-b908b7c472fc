<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_graphicpage_info" author="wiliam"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="GraphicPage" remarks="graphicpage info table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Type" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="GroupId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="PageCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BaseEquipmentId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="AppendName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="TemplateId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Data" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="Style" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="Children" type="longtext">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="RouterType" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="CrumbsRouterType" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="PageCanFullScreen" type="tinyint(1)">
                <constraints nullable="true"/>
            </column>
            <column name="SceneId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="IsDefault" type="tinyint(1)" remarks="是否默认组态页">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="最新修改时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="GraphicPage" columnName="Id" incrementBy="1"  columnDataType ="int" startWith="1" />
        <createIndex tableName="GraphicPage" indexName="IDX_GraphicPage_1" unique="false">
            <column name="PageCategory"></column>
            <column name="ObjectId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>