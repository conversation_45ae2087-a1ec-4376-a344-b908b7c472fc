<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_link_node_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="LinkNode"
                     remarks="linkage node table">
            <column name="NodeId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="LinkElementConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="NodeDirection" type="varchar(10)">
                <constraints nullable="false"/>
            </column>
            <column name="NodeType" type="varchar(64)">
                <constraints nullable="false"/>
            </column>
            <column name="NodeIndex" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="NodeTag" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="varchar(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="LinkNode" columnName="NodeId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>