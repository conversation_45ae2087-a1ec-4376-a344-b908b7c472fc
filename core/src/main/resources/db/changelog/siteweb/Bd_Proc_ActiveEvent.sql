DROP PROCEDURE IF EXISTS Bd_S6_ConfirmedEvent;

CREATE PROCEDURE `Bd_S6_ConfirmedEvent`(

    IN  Events     VARCHAR(4000),
    IN  ConfirmerId        int,
    in  Note   VARCHAR(255),
    IN EventReasonType int
)
BEGIN
    DECLARE   v_EndTime        datetime     ;
    DECLARE   v_SequenceId     VARCHAR(256);
    DECLARE   v_ConfirmTime     datetime   ;
    DECLARE   v_ConfirmerName    VARCHAR(256);
    DECLARE   v_i                 INT ;
    DECLARE   v_RecordCount       INT ;
    DECLARE   v_sql       VARCHAR(4000);
    DECLARE   v_OldConfirmTime dateTime;
    DECLARE   v_Description    VARCHAR(255);

    DROP TEMPORARY TABLE IF EXISTS tt_ConfirmAlarm;
    CREATE TEMPORARY TABLE tt_ConfirmAlarm (
                                               IndexId INT AUTO_INCREMENT UNIQUE,
                                               SequenceId VARCHAR(128) NOT NULL,
                                               StationId INT NOT NULL,
                                               StationName VARCHAR(255)  NULL,
                                               EquipmentId INT  NULL,
                                               EquipmentName VARCHAR(128)  NULL,
                                               EventId INT  NULL,
                                               EventName VARCHAR(128)  NULL,
                                               EventConditionId INT  NULL,
                                               EventSeverityId INT  NULL,
                                               EventSeverity VARCHAR(128) NULL,
                                               EventLevel INT NULL,
                                               StartTime DATETIME  NULL,
                                               EndTime DATETIME NULL,
                                               CancelTime DATETIME NULL,
                                               CancelUserId INT NULL,
                                               CancelUserName VARCHAR(128) NULL,
                                               ConfirmTime DATETIME NULL,
                                               ConfirmerId INT NULL,
                                               ConfirmerName VARCHAR(128) NULL,
                                               EventValue FLOAT NULL,
                                               EndValue DOUBLE NULL,
                                               ReversalNum INT NULL,
                                               Meanings VARCHAR(255) NULL,
                                               EventFilePath VARCHAR(255) NULL,
                                               Description VARCHAR(255) NULL,
                                               SourceHostId INT NULL,
                                               InstructionId VARCHAR(255) NULL,
                                               InstructionStatus INT NULL,
                                               StandardAlarmNameId INT NULL,
                                               StandardAlarmName VARCHAR(128) NULL,
                                               BaseTypeId NUMERIC(10,0) NULL,
                                               BaseTypeName VARCHAR(128) NULL,
                                               EquipmentCategory INT  NULL,
                                               EquipmentCategoryName VARCHAR(128) NULL,
                                               MaintainState INT NOT NULL,
                                               SignalId INT NULL,
                                               RelateSequenceId VARCHAR(128) NULL,
                                               EventCategoryId INT NULL,
                                               EventStateId INT NULL,
                                               CenterId INT NULL,
                                               CenterName VARCHAR(128) NULL,
                                               StructureName VARCHAR(128) NULL,
                                               MonitorUnitName VARCHAR(128) NULL,
                                               StructureId INT NULL,
                                               StationCategoryId INT NULL,
                                               EquipmentVendor  VARCHAR(128) NULL,
                                               resourcestructureId   INT  DEFAULT 0,
                                               BaseEquipmentId       INT DEFAULT 0,
                                               ConvergenceEventId    BIGINT DEFAULT 0,
                                               EventReasonType int DEFAULT NULL
    )  ;

    SET v_sql = CONCAT('INSERT INTO tt_ConfirmAlarm(SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, EndValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId,StationCategoryId,EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId,EventReasonType)
	   SELECT SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, EndValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId,StationCategoryId,EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId,EventReasonType
	   					     	FROM  TBL_ActiveEvent WHERE SequenceId IN (' , IFNULL(Events,'') , ')');
    SET @sql_Stmt = v_sql;
    PREPARE Execute_Stmt FROM @sql_Stmt;
    EXECUTE Execute_Stmt;
    DEALLOCATE PREPARE Execute_Stmt;

    SET v_ConfirmTime = CURRENT_TIMESTAMP ;
    SELECT UserName INTO v_ConfirmerName FROM TBL_Account WHERE UserId = ConfirmerId ;

    SELECT  MIN(IndexId), MAX(IndexId) INTO v_i,v_RecordCount FROM tt_ConfirmAlarm;
    WHILE v_i <= v_RecordCount DO
            SELECT ConfirmTime,SequenceId,Description INTO v_OldConfirmTime,v_SequenceId,v_Description
            FROM tt_ConfirmAlarm
            WHERE IndexId = v_i;

            SELECT EndTime INTO v_EndTime FROM TBL_ActiveEvent WHERE SequenceId = v_SequenceId;

            IF v_OldConfirmTime IS NULL THEN

                IF v_EndTime IS NULL THEN
                    UPDATE TBL_ActiveEvent T SET T.ConfirmTime = v_ConfirmTime, T.ConfirmerId = ConfirmerId, T.ConfirmerName = v_ConfirmerName,T.Description = getEventNote(T.Description,Note), T.EventReasonType = EventReasonType
                    WHERE SequenceId = v_SequenceId;
                ELSE
                    IF Note IS NOT NULL AND Note != '' THEN
                            UPDATE TBL_ActiveEvent T
                            SET T.Description = getEventNote(T.Description, Note)
                            WHERE SequenceId = v_SequenceId;
                    END IF;
                    CALL BD_PNL_Ins_MidHistoryEvent(v_SequenceId, v_EndTime, v_ConfirmTime, ConfirmerId, v_ConfirmerName, EventReasonType, @ret);
                END IF;

                INSERT INTO TBL_AlarmChange
                (
                    SequenceId ,
                    OperationType ,
                    StationId ,
                    StationName ,
                    EquipmentId ,
                    EquipmentName ,
                    EventId ,
                    EventName ,
                    EventConditionId ,
                    EventSeverityId ,
                    EventSeverity ,
                    EventLevel,
                    StartTime ,
                    EndTime ,
                    CancelTime ,
                    CancelUserId ,
                    CancelUserName ,
                    ConfirmTime ,
                    ConfirmerId ,
                    ConfirmerName ,
                    EventValue ,
                    EndValue,
                    ReversalNum ,
                    Meanings ,
                    EventFilePath ,
                    Description ,
                    SourceHostId ,
                    InstructionId ,
                    InstructionStatus ,
                    StandardAlarmNameId ,
                    StandardAlarmName ,
                    BaseTypeId ,
                    BaseTypeName ,
                    EquipmentCategory ,
                    EquipmentCategoryName ,
                    MaintainState  ,
                    SignalId ,
                    RelateSequenceId ,
                    EventCategoryId ,
                    EventStateId ,
                    CenterId ,
                    CenterName ,
                    StructureName ,
                    MonitorUnitName,
                    StructureId,
                    StationCategoryId,
                    EquipmentVendor,
                    resourcestructureId,
                    BaseEquipmentId,
                    ConvergenceEventId,
                    EventReasonType
                )
                SELECT
                    T.SequenceId,
                    3 AS OperationType,
                    T.StationId,
                    T.StationName,
                    T.EquipmentId,
                    T.EquipmentName,
                    T.EventId,
                    T.EventName,
                    T.EventConditionId,
                    T.EventSeverityId,
                    T.EventSeverity,
                    T.EventLevel,
                    T.StartTime,
                    v_EndTime,
                    T.CancelTime,
                    T.CancelUserId,
                    T.CancelUserName,
                    v_ConfirmTime,
                    ConfirmerId,
                    v_ConfirmerName,
                    T.EventValue,
                    T.EndValue,
                    T.ReversalNum,
                    T.Meanings,
                    T.EventFilePath,
                    getEventNote(v_Description,Note),
                    T.SourceHostId,
                    T.InstructionId,
                    T.InstructionStatus,
                    T.StandardAlarmNameId,
                    T.StandardAlarmName,
                    T.BaseTypeId,
                    T.BaseTypeName,
                    T.EquipmentCategory,
                    T.EquipmentCategoryName,
                    T.MaintainState,
                    T.SignalId,
                    T.RelateSequenceId,
                    T.EventCategoryId,
                    T.EventStateId,
                    T.CenterId,
                    T.CenterName,
                    T.StructureName,
                    T.MonitorUnitName,
                    T.StructureId,
                    T.StationCategoryId,
                    T.EquipmentVendor,
                    T.resourcestructureId,
                    T.BaseEquipmentId,
                    T.ConvergenceEventId,
                    EventReasonType
                FROM tt_ConfirmAlarm T
                WHERE T.SequenceId = v_SequenceId;
            END IF;

            SET v_i = v_i + 1;
        END WHILE;
    DROP TEMPORARY TABLE IF EXISTS tt_ConfirmAlarm;
END;


DROP PROCEDURE IF EXISTS BD_S6_CancelEvent;

CREATE PROCEDURE `BD_S6_CancelEvent`(IN Events varchar(4000), IN UserId int, IN Note varchar(4000))
BEGIN
    DECLARE     v_SequenceId     VARCHAR(256);
    DECLARE     v_ConfirmTime    datetime;
    DECLARE     v_ConfirmerId    int;
    DECLARE     v_StationId		int;
    DECLARE     v_EquipmentId	int;
    DECLARE     v_EventId	    int;
    DECLARE     v_EventConditionId int;
    DECLARE     v_StartTime		DATETIME;
    DECLARE     v_EndTime		DATETIME;
    DECLARE     v_Overturn		INT;
    DECLARE     v_Meanings		VARCHAR(255);
    DECLARE     v_EventValue	FLOAT;
    DECLARE     v_BaseTypeId	NUMERIC(10,0);
    DECLARE     v_ConfirmerName  VARCHAR(256);
    DECLARE     v_UserName       VARCHAR(256);
    DECLARE     v_i                 INT ;
    DECLARE     v_RecordCount       INT ;
    DECLARE     v_OperationType     INT ;
    DECLARE     v_sql       VARCHAR(4000);
    DECLARE     v_isProcess				INT;

    DECLARE     v_SourceHostId	INT;
    DECLARE     v_EventName		VARCHAR(128);
    DECLARE     v_acEquipmentName VARCHAR(128);
    DECLARE     v_EquipmentCategoryId INT;
   	DECLARE     v_EventReasonType     INT;

    DROP TEMPORARY TABLE IF EXISTS tt_CancelAlarm;
    CREATE TEMPORARY TABLE tt_CancelAlarm (
                                              IndexId INT AUTO_INCREMENT UNIQUE,
                                              SequenceId VARCHAR(128) NOT NULL,
                                              StationId INT NOT NULL,
                                              StationName VARCHAR(255)  NULL,
                                              EquipmentId INT  NULL,
                                              EquipmentName VARCHAR(128)  NULL,
                                              EventId INT  NULL,
                                              EventName VARCHAR(128)  NULL,
                                              EventConditionId INT  NULL,
                                              EventSeverityId INT  NULL,
                                              EventSeverity VARCHAR(128) NULL,
                                              EventLevel INT NULL,
                                              StartTime DATETIME  NULL,
                                              EndTime DATETIME NULL,
                                              CancelTime DATETIME NULL,
                                              CancelUserId INT NULL,
                                              CancelUserName VARCHAR(128) NULL,
                                              ConfirmTime DATETIME NULL,
                                              ConfirmerId INT NULL,
                                              ConfirmerName VARCHAR(128) NULL,
                                              EventValue FLOAT NULL,
                                              ReversalNum INT NULL,
                                              Meanings VARCHAR(255) NULL,
                                              EventFilePath VARCHAR(255) NULL,
                                              Description VARCHAR(255) NULL,
                                              SourceHostId INT NULL,
                                              InstructionId VARCHAR(255) NULL,
                                              InstructionStatus INT NULL,
                                              StandardAlarmNameId INT NULL,
                                              StandardAlarmName VARCHAR(128) NULL,
                                              BaseTypeId NUMERIC(10,0) NULL,
                                              BaseTypeName VARCHAR(128) NULL,
                                              EquipmentCategory INT  NULL,
                                              EquipmentCategoryName VARCHAR(128) NULL,
                                              MaintainState INT NOT NULL,
                                              SignalId INT NULL,
                                              RelateSequenceId VARCHAR(128) NULL,
                                              EventCategoryId INT NULL,
                                              EventStateId INT NULL,
                                              CenterId INT NULL,
                                              CenterName VARCHAR(128) NULL,
                                              StructureName VARCHAR(128) NULL,
                                              MonitorUnitName VARCHAR(128) NULL,
                                              StructureId INT NULL,
                                              StationCategoryId INT NULL,
                                              EquipmentVendor  VARCHAR(128) NULL,
                                              resourcestructureId   INT  DEFAULT 0,
                                              BaseEquipmentId       INT DEFAULT 0,
                                              ConvergenceEventId    BIGINT DEFAULT 0,
                                              EventReasonType int DEFAULT NULL
    );

    SET v_sql = CONCAT('INSERT INTO tt_CancelAlarm(SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId, StationCategoryId, EquipmentVendor, resourcestructureId,BaseEquipmentId,ConvergenceEventId,EventReasonType)
	   SELECT SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId, StationCategoryId, EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId,EventReasonType
	   					     	FROM  TBL_ActiveEvent WHERE SequenceId IN (' , IFNULL(Events,'') , ') and EndTime is null');

    SET @sql_Stmt = v_sql;
    PREPARE Execute_Stmt FROM @sql_Stmt;
    EXECUTE Execute_Stmt;
    DEALLOCATE PREPARE Execute_Stmt;

    SET v_EndTime = CURRENT_TIMESTAMP;

    SELECT T.UserName into v_UserName FROM TBL_Account T WHERE T.UserId = UserId;

    SELECT MIN(IndexId), MAX(IndexId) INTO v_i , v_RecordCount   FROM tt_CancelAlarm;
    WHILE v_i <= v_RecordCount DO
            SELECT 	T.StationId,
                      T.EquipmentId,
                      T.EventId,
                      T.EventConditionId,
                      T.SequenceId,
                      T.StartTime,
                      T.ReversalNum,
                      T.Meanings,
                      T.EventValue,
                      T.BaseTypeId,
                      T.ConfirmTime ,
                      T.ConfirmerId,
                      T.ConfirmerName,
                      T.SourceHostId,
                      T.EquipmentCategory,
                      T.EventName,
                      T.EventReasonType
            INTO
                v_StationId              ,
                v_EquipmentId            ,
                v_EventId                ,
                v_EventConditionId       ,
                v_SequenceId             ,
                v_StartTime              ,
                v_Overturn               ,
                v_Meanings               ,
                v_EventValue             ,
                v_BaseTypeId             ,
                v_ConfirmTime            ,
                v_ConfirmerId            ,
                v_ConfirmerName          ,
                v_SourceHostId           ,
                v_EquipmentCategoryId    ,
                v_EventName,
                v_EventReasonType
            FROM tt_CancelAlarm T WHERE T.IndexId = v_i;


            IF (v_EventId = -3 AND v_EndTime IS NOT NULL) THEN
                UPDATE TBL_Equipment T
                SET ConnectState = 1
                WHERE StationId = v_StationId AND EquipmentId = v_EquipmentId;
            END IF;


            IF EXISTS(SELECT 'X' FROM TBL_SysConfig WHERE ConfigKey = 'StandardCategory' AND ConfigValue = '1') THEN
                IF (v_EquipmentCategoryId = 99 AND v_Meanings = '被监控设备通信采集中断') THEN
                    SET v_acEquipmentName = LEFT(v_EventName, char_length(v_EventName) - instr(reverse(v_EventName),'_'));

                    UPDATE TBL_Equipment
                    SET ConnectState = 1
                    WHERE StationId = v_StationId AND EquipmentName = v_acEquipmentName AND MonitorUnitId = v_SourceHostId;
                END	IF;
            END IF;


            SELECT T.IsProcess INTO v_isProcess FROM TBL_SARIsProcess T;


            CALL PNL_SavePreEventResponse(v_StationId,v_EquipmentId,v_EventId,v_EventConditionId,v_SequenceId,v_StartTime,v_EndTime,v_Overturn,v_Meanings,v_EventValue,v_BaseTypeId,@ret);


            IF v_isProcess  > 1	 THEN

                DELETE FROM TBL_SARAlarmQueue WHERE SequenceId = v_SequenceId;

                UPDATE TBL_SARAlarmActiveRecord
                SET EndTime = v_EndTime,Overturn = v_Overturn,Meanings = v_Meanings, EventValue = v_EventValue
                WHERE SequenceId = v_SequenceId;
            END	IF;


            UPDATE TBL_ActiveEvent SET CancelTime = v_EndTime, CancelUserId = UserId, CancelUserName = v_UserName, Description =  getEventNote(Description,Note)
            WHERE SequenceId =  v_SequenceId;

            IF v_ConfirmTime IS NULL THEN
                SET v_ConfirmTime = CURRENT_TIMESTAMP ;
                SET v_ConfirmerName = v_UserName;
                SET v_ConfirmerId = UserId;
            END IF;


            CALL BD_PNL_Ins_MidHistoryEvent(v_SequenceId, v_EndTime, v_ConfirmTime, v_ConfirmerId, v_ConfirmerName, v_EventReasonType, @ret);

            INSERT INTO TBL_AlarmChange
            (  SequenceId ,
               OperationType ,
               StationId ,
               StationName ,
               EquipmentId ,
               EquipmentName ,
               EventId ,
               EventName ,
               EventConditionId ,
               EventSeverityId ,
               EventSeverity ,
               EventLevel,
               StartTime ,
               EndTime ,
               CancelTime ,
               CancelUserId ,
               CancelUserName ,
               ConfirmTime ,
               ConfirmerId ,
               ConfirmerName ,
               EventValue ,
               ReversalNum ,
               Meanings ,
               EventFilePath ,
               Description ,
               SourceHostId ,
               InstructionId ,
               InstructionStatus ,
               StandardAlarmNameId ,
               StandardAlarmName ,
               BaseTypeId ,
               BaseTypeName ,
               EquipmentCategory ,
               EquipmentCategoryName ,
               MaintainState  ,
               SignalId ,
               RelateSequenceId ,
               EventCategoryId ,
               EventStateId ,
               CenterId ,
               CenterName ,
               StructureName ,
               MonitorUnitName,
               StructureId,
               StationCategoryId,
               EquipmentVendor,
               resourcestructureId,
               BaseEquipmentId,
               ConvergenceEventId,
               EventReasonType
            )
            SELECT
                T.SequenceId,
                2 AS OperationType,
                T.StationId,
                T.StationName,
                T.EquipmentId,
                T.EquipmentName,
                T.EventId,
                T.EventName,
                T.EventConditionId,
                T.EventSeverityId,
                T.EventSeverity,
                T.EventLevel,
                T.StartTime,
                v_EndTime,
                v_EndTime,
                UserId,
                v_UserName,
                T.ConfirmTime,
                T.ConfirmerId,
                T.ConfirmerName,
                T.EventValue,
                T.ReversalNum,
                T.Meanings,
                T.EventFilePath,
                getEventNote(T.Description,Note),
                T.SourceHostId,
                T.InstructionId,
                T.InstructionStatus,
                T.StandardAlarmNameId,
                T.StandardAlarmName,
                T.BaseTypeId,
                T.BaseTypeName,
                T.EquipmentCategory,
                T.EquipmentCategoryName,
                T.MaintainState,
                T.SignalId,
                T.RelateSequenceId,
                T.EventCategoryId,
                T.EventStateId,
                T.CenterId,
                T.CenterName,
                T.StructureName,
                T.MonitorUnitName,
                T.StructureId,
                T.StationCategoryId,
                T.EquipmentVendor,
                T.resourcestructureId,
                T.BaseEquipmentId,
                T.ConvergenceEventId,
                T.EventReasonType
            FROM tt_CancelAlarm T
            WHERE T.SequenceId = v_SequenceId;



            INSERT INTO TBL_AlarmChange
            (
                SequenceId ,
                OperationType ,
                StationId ,
                StationName ,
                EquipmentId ,
                EquipmentName ,
                EventId ,
                EventName ,
                EventConditionId ,
                EventSeverityId ,
                EventSeverity ,
                EventLevel,
                StartTime ,
                EndTime ,
                CancelTime ,
                CancelUserId ,
                CancelUserName ,
                ConfirmTime ,
                ConfirmerId ,
                ConfirmerName ,
                EventValue ,
                ReversalNum ,
                Meanings ,
                EventFilePath ,
                Description ,
                SourceHostId ,
                InstructionId ,
                InstructionStatus ,
                StandardAlarmNameId ,
                StandardAlarmName ,
                BaseTypeId ,
                BaseTypeName ,
                EquipmentCategory ,
                EquipmentCategoryName ,
                MaintainState  ,
                SignalId ,
                RelateSequenceId ,
                EventCategoryId ,
                EventStateId ,
                CenterId ,
                CenterName ,
                StructureName ,
                MonitorUnitName,
                StructureId,
                StationCategoryId,
                EquipmentVendor,
                resourcestructureId,
                BaseEquipmentId,
                ConvergenceEventId,
                EventReasonType
            )
            SELECT
                T.SequenceId,
                3 AS OperationType,
                T.StationId,
                T.StationName,
                T.EquipmentId,
                T.EquipmentName,
                T.EventId,
                T.EventName,
                T.EventConditionId,
                T.EventSeverityId,
                T.EventSeverity,
                T.EventLevel,
                T.StartTime,
                v_EndTime,
                v_EndTime,
                UserId,
                v_UserName,
                v_ConfirmTime,
                v_ConfirmerId,
                v_ConfirmerName,
                T.EventValue,
                T.ReversalNum,
                T.Meanings,
                T.EventFilePath,
                getEventNote(T.Description,Note),
                T.SourceHostId,
                T.InstructionId,
                T.InstructionStatus,
                T.StandardAlarmNameId,
                T.StandardAlarmName,
                T.BaseTypeId,
                T.BaseTypeName,
                T.EquipmentCategory,
                T.EquipmentCategoryName,
                T.MaintainState,
                T.SignalId,
                T.RelateSequenceId,
                T.EventCategoryId,
                T.EventStateId,
                T.CenterId,
                T.CenterName,
                T.StructureName,
                T.MonitorUnitName,
                T.StructureId,
                T.StationCategoryId,
                T.EquipmentVendor,
                T.resourcestructureId,
                T.BaseEquipmentId,
                T.ConvergenceEventId,
                T.EventReasonType
            FROM tt_CancelAlarm T
            WHERE T.SequenceId = v_SequenceId;

            SET v_i = v_i + 1;
        END WHILE;
    DROP TEMPORARY TABLE IF EXISTS tt_CancelAlarm;
END;

DROP PROCEDURE IF EXISTS BD_PNL_Ins_MidHistoryEvent;

CREATE PROCEDURE `BD_PNL_Ins_MidHistoryEvent`(

	IN  SequenceId             varchar(255),
	IN  EndTime                DATETIME	,
	IN  ConfirmTime		       DATETIME  ,
	IN  ConfirmerId            INT     ,
	IN  ConfirmerName          varchar(255),
	IN  EventReasonType        INT,
	OUT ret INT)
L_return:
BEGIN

    DECLARE    v_UserId    int;
    DECLARE    v_UserName  varchar(256);
	DECLARE    v_StartTime   datetime;
	DECLARE    v_ReversalNum int;
    DECLARE    v_Description varchar(255);
    DECLARE    v_EndValue    double;

	DECLARE    SWV_Error   INT DEFAULT 0;
	DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
	BEGIN
	  SET SWV_Error = -1;
	END;

	SET v_StartTime = null;

	SELECT T.StartTime, T.ReversalNum, T.Description, T.EndValue
    INTO v_StartTime, v_ReversalNum, v_Description, v_EndValue
	FROM TBL_ActiveEvent T WHERE T.SequenceId = SequenceId Limit 1;

	IF v_ReversalNum > 0 AND v_StartTime IS NOT NULL THEN


			IF EXISTS(SELECT 'X' FROM TBL_HistoryEvent T
				WHERE T.StartTime > date_add(v_StartTime, INTERVAL -1 SECOND) AND T.StartTime < date_add(v_StartTime, INTERVAL 1 SECOND)
					AND T.SequenceId = SequenceId) THEN

				UPDATE TBL_HistoryEvent T SET ReversalNum = v_ReversalNum,
					T.Description = CASE
						WHEN v_Description is not null THEN concat(T.Description,' [', v_ReversalNum, ':', v_Description, ']')
                        ELSE T.Description
                        END ,
                    T.EndValue = v_EndValue,
					T.EndTime = EndTime, T.ConfirmerId = ConfirmerId, T.ConfirmerName = ConfirmerName, T.ConfirmTime = ConfirmTime, T.EventReasonType = EventReasonType
				WHERE T.StartTime >  date_add(v_StartTime, INTERVAL -1 SECOND) AND T.StartTime < date_add(v_StartTime, INTERVAL 1 SECOND)
					AND T.SequenceId = SequenceId;

				DELETE FROM TBL_ActiveEvent t WHERE  t.SequenceId = SequenceId;

				IF SWV_Error<> 0 then
					SET ret = -1;
					LEAVE L_return;
				END IF;


				SET ret = 0;
				LEAVE L_return;

			END IF;
	END IF;

	INSERT INTO TBL_HistoryEvent
					(SequenceId,
					StationId ,
					StationName,
					EquipmentId,
					EquipmentName ,
					EventId,
					EventName,
					EventConditionId,
					EventSeverityId,
					EventSeverity,
					EventLevel,
					StartTime,
					EndTime,
					CancelTime,
					CancelUserId,
					CancelUserName ,
					ConfirmTime,
					ConfirmerId,
					ConfirmerName,
					EventValue ,
					EndValue,
					ReversalNum,
					Meanings ,
					EventFilePath ,
					Description,
					SourceHostId ,
					InstructionId ,
					InstructionStatus,
					StandardAlarmNameId ,
					StandardAlarmName,
					BaseTypeId,
					BaseTypeName ,
					EquipmentCategory ,
					EquipmentCategoryName,
					MaintainState ,
					SignalId ,
					RelateSequenceId,
					EventCategoryId,
					EventStateId,
					CenterId,
					CenterName,
					StructureName,
					MonitorUnitName,
					StructureId,
					StationCategoryId,
					EquipmentVendor,
					resourcestructureId,
					BaseEquipmentId,
					ConvergenceEventId,
					EventReasonType)
			 SELECT t.SequenceId,
					t.StationId ,
					t.StationName,
					t.EquipmentId,
					t.EquipmentName ,
					t.EventId,
					t.EventName,
					t.EventConditionId,
					t.EventSeverityId,
					t.EventSeverity,
					t.EventLevel,
					t.StartTime,
					EndTime,
					t.CancelTime,
					t.CancelUserId,
					t.CancelUserName ,
					ConfirmTime,
					ConfirmerId,
					ConfirmerName,
					t.EventValue ,
					t.EndValue,
					t.ReversalNum,
					t.Meanings ,
					t.EventFilePath ,
					t.Description,
					t.SourceHostId ,
					t.InstructionId ,
					t.InstructionStatus,
					t.StandardAlarmNameId ,
					t.StandardAlarmName,
					t.BaseTypeId,
					t.BaseTypeName ,
					t.EquipmentCategory ,
					t.EquipmentCategoryName,
					t.MaintainState ,
					t.SignalId ,
					t.RelateSequenceId,
					t.EventCategoryId,
					t.EventStateId,
					t.CenterId,
					t.CenterName,
					t.StructureName,
					t.MonitorUnitName,
					t.StructureId,
					t.StationCategoryId,
					t.EquipmentVendor,
					t.resourcestructureId,
					t.BaseEquipmentId,
					t.ConvergenceEventId,
					EventReasonType
			 FROM  TBL_ActiveEvent t
			 WHERE t.SequenceId = SequenceId;

	DELETE FROM TBL_ActiveEvent t WHERE  t.SequenceId = SequenceId;

	IF SWV_Error<> 0 then
		SET ret = -1;
		LEAVE L_return;
	END IF;


	SET ret = 0;
	LEAVE L_return;
END;

