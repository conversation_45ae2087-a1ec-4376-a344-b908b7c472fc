<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblDataEntry_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="tbl_dataentry" remarks="Data Dictionary Entry">
            <column name="EntryId" type="INT" remarks="字典ID">
                <constraints nullable="false" primaryKey="true" unique="true"/>
            </column>
            <column name="EntryCategory" type="INT" remarks="字典类型"/>
            <column name="EntryName" type="VARCHAR(128)" remarks="字典名"/>
            <column name="EntryTitle" type="VARCHAR(128)" remarks="字典标题"/>
            <column name="EntryAlias" type="VARCHAR(255)" remarks="字典英文名"/>
            <column defaultValue="1" name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息"/>
        </createTable>
    </changeSet>
</databaseChangeLog>