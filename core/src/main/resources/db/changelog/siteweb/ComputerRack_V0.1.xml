<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_computerRack_info" author="Liu.Yandong">
        <createTable tableName="ComputerRack" remarks="computerRack info table">
            <column name="ComputerRackId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ComputerRackName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ComputerRackNumber" type="varchar(128)">
                <constraints nullable="true" unique="true"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Position" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Customer" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Business" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComputerRack" columnName="ComputerRackId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>