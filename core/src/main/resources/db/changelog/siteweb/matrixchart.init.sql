/*组态控件自定义api接口
   1-100 除了1，2，3，4，5 其余给s2使用
   100-200 给phoenix使用
   其余后面自己定义区间
*/
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (1,'监控总览','stationhaltpoweroffgen',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (2,'站点总览','stationcategorylist',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (3,'告警TOP5','stationeventtopfive',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (4,'监控统计','devicemonitoringstatus',NULL);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (5,'告警趋势','alarmgrouptrends',2);
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (6,'区域告警分布','eventdistribute',1); --
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (7,'月度告警统计','monthevent',1); --
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (9,'断站区域分布','stninterruptdistribute',1); --
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (10,'停电区域分布','stnpoweroffdistribute',1); --
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (11,'局站区域统计','stnpoweroffdistribute',1); --
# insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (12,'电池总电压低区域分布','batlowvoltage',1); --
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (100,'容量负载','capacity/percentcount',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (101,'指标百分比统计','livecomplexindexs/percentcount',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (201,'局站分类统计','stationstatistics',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (202,'电信版告警趋势','alarmtrend',2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (203,'Top10局站告警统计','stationalarmstatistics',2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (204,'关键告警统计','importanteventstatistics',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (205,'监控可用度','structureavailability',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (206, '大楼告警数量统计', 'buildingalarmstatistics', 3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (207, '大楼设备类型告警数量统计', 'equipmentcategoryalarmstatistics', 3);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`,DataType) VALUES (208, '安全指数近30天曲线', 'rw/getdata?action=safe_index_30', 3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (220, '房间机架统计', 'computerrack/capacitybyroom', 2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (221, '大楼机架统计', 'computerrack/capacitybybuilding', 2);
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (401,'机器人本身业务统计','Robot/RobotOwnStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (402,'机器人告警级别统计','RobotAlarm/AlarmStatisticsByLevel','3');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (403,'机器人设备告警数时间统计','RobotAlarm/AlarmStatisticsByTime','3');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (404,'机器人环境统计','Robot/EnvironmentDataStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (405,'机器人巡检点最高最低温度统计','RobotAlarm/TemperatureHLStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (406,'机器人设备统计','RobotDevice/DeviceStatistics','2');
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(407, '监控总览地市', '/stationhaltpoweroffgen/102', 1);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(408, '监控总览区县', '/stationhaltpoweroffgen/103', 1);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(409, '当日安全指数', 'rw/getdata?action=rador_index', 3);



