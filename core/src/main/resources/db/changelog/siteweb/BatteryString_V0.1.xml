<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_batteryString_item" author="habits" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="BatteryString"
                     remarks="BatteryString table">
            <column name="BatteryStringId" type="int" remarks="电池配置ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BatteryStringName" type="varchar(128)" remarks="电池配置名称">
                <constraints nullable="false"/>
            </column>
            <column name="CellCount" type="int" remarks="电池节数量">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="BatteryCellModelId" type="int" remarks="电池模型ID">
                <constraints nullable="false"/>
            </column>
            <column name="StandbyPower" type="int" remarks="备电时间">
                <constraints nullable="true"/>
            </column>
            <column name="StartUsingTime" type="datetime" remarks="启用时间">
                <constraints nullable="true"/>
            </column>
            <column name="CurrentTransformerType" type="varchar(255)" remarks="电流互感器类型">
                <constraints nullable="true"/>
            </column>
            <column name="Vendor" type="varchar(255)" remarks="厂家">
                <constraints nullable="true"/>
            </column>
            <column name="Model" type="varchar(255)" remarks="型号">
                <constraints nullable="true"/>
            </column>
            <column name="RatedVoltage" type="decimal(5,2)" remarks="额定电压">
                <constraints nullable="true"/>
            </column>
            <column name="RatedCapacity" type="decimal(6,2)" remarks="额定容量">
                <constraints nullable="true"/>
            </column>
            <column name="InitialIR" type="decimal(10,2)" remarks="初始内阻">
                <constraints nullable="true"/>
            </column>
            <column name="FloatChargeVoltage" type="decimal(5,2)" remarks="浮充电压">
                <constraints nullable="true"/>
            </column>
            <column name="EvenChargeVoltage" type="decimal(5,2)" remarks="均充电压">
                <constraints nullable="true"/>
            </column>
            <column name="TempCompensationFactor" type="decimal(3,2)" remarks="温度补偿系数">
                <constraints nullable="true"/>
            </column>
            <column name="TerminationVoltage" type="decimal(10,2)" remarks="终止电压">
                <constraints nullable="true"/>
            </column>
            <column name="MaxChargingCurrent" type="decimal(5,2)" remarks="最大均充充电电流">
                <constraints nullable="true"/>
            </column>
            <column name="MaxFloatChargeVoltage" type="decimal(10,2)" remarks="最大浮充电压">
                <constraints nullable="true"/>
            </column>
            <column name="Weight" type="decimal(10,2)" remarks="重量">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="BatteryString" columnName="BatteryStringId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>