<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="License" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="License" remarks="授权码信息表">
            <column name="LicenseId" type="int" remarks="授权码主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Product" type="varchar(255)" remarks="产品名称">
                <constraints nullable="false" />
            </column>
            <column name="UniqueInfo" type="varchar(1000)" remarks="系统唯一uuid">
                <constraints nullable="true" />
            </column>
            <column name="LicenseType" type="int" remarks="授权码类型 1试用授权码 2正式授权码">
                <constraints nullable="false" />
            </column>
            <column name="ActiveTime" type="datetime" remarks="激活时间">
                <constraints nullable="false" />
            </column>
            <column name="LimitTime" type="datetime" remarks="到期时间">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="License" columnName="LicenseId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>