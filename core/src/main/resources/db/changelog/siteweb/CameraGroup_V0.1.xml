<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="CameraGroup" author="liaoximing" >
        <createTable tableName="CameraGroup"
                     remarks="摄像头分组信息表">
            <column name="CameraGroupId" type="int" remarks="分组主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CameraGroupName" type="varchar(255)" remarks="摄像头分组名称">
                <constraints nullable="true" />
            </column>
            <column name="ParentId" type="int" remarks="父级id">
                <constraints nullable="true" />
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="CameraGroup" columnName="CameraGroupId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>