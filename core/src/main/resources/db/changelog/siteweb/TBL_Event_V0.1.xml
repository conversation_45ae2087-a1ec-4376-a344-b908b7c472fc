<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblEvent_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Event" remarks="Event info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="int" remarks="告警ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventName" type="varchar(128)" remarks="告警名">
                <constraints nullable="false"/>
            </column>
            <column name="StartType" type="int" remarks="开始类型">
                <constraints nullable="false"/>
            </column>
            <column name="EndType" type="int" remarks="结束类型">
                <constraints nullable="false"/>
            </column>
            <column name="StartExpression" type="varchar(1024)" remarks="告警开始表达式">
                <constraints nullable="true"/>
            </column>
            <column name="SuppressExpression" type="varchar(1024)" remarks="告警抑制表达式">
                <constraints nullable="true"/>
            </column>
            <column name="EventCategory" type="int" remarks="告警分类（分标准，内部用）">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int" remarks="关联信号ID">
                <constraints nullable="true"/>
            </column>
            <column name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="Visible" type="tinyint(1)" remarks="是否可视">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="DisplayIndex" type="int" remarks="显示顺序">
                <constraints nullable="true"/>
            </column>
            <column name="ModuleNo" type="int" remarks="模块号">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_Event" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TBL_Event" indexName="IDX_Event_Id" unique="true">
            <column name="EquipmentTemplateId"></column>
            <column name="EventId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>