<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="WebClientLog" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="WebClientLog" remarks="前端日志记录表">
            <column name="WebClientLogId" type="int" remarks="主键自增id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserName" type="varchar(255)" remarks="用户名称">
                <constraints nullable="true" />
            </column>
            <column name="ClientIp" type="varchar(255)" remarks="客户端ip">
                <constraints nullable="true" />
            </column>
            <column name="BusinessModule" type="varchar(50)" remarks="模块类型">
                <constraints nullable="true" />
            </column>
            <column name="Content" type="varchar(1000)" remarks="内容">
                <constraints nullable="true" />
            </column>
            <column name="Remark" type="varchar(255)" remarks="备注">
                <constraints nullable="true" />
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="WebClientLog" columnName="WebClientLogId" incrementBy="1"  columnDataType="int" startWith="1" />
        <createIndex tableName="WebClientLog" indexName="IDX_CreateTime">
            <column name="CreateTime"/>
        </createIndex>
        <createIndex tableName="WebClientLog" indexName="IDX_UserName_CreateTime">
            <column name="UserName"/>
            <column name="CreateTime"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>