<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create-TtsStrategy-table" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TtsStrategy" remarks="TTS策略表">
            <column name="TtsStrategyId" type="int" remarks="主键自增id">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="StrategyName" type="varchar(50)" remarks="策略名称">
                <constraints nullable="true"/>
            </column>
            <column name="Enable" type="int" defaultValueNumeric="0" remarks="是否启用 0 否 1是">
                <constraints nullable="true"/>
            </column>
            <column name="EffectiveStartTime" type="datetime" remarks="生效开始时间">
                <constraints nullable="true"/>
            </column>
            <column name="EffectiveEndTime" type="datetime" remarks="生效结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="TtsStrategy" columnName="TtsStrategyId" startWith="1" incrementBy="1" columnDataType="int"/>
    </changeSet>

</databaseChangeLog>
