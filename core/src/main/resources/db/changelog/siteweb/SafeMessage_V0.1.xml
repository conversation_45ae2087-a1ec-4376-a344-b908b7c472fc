<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="SafeMessage" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="SafeMessage" remarks="SafeMessage info table">
            <column name="SafeMessageId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ConfigName" type="varchar(128)" remarks="平安短信配置名称">
                <constraints nullable="false" />
            </column>
            <column name="Receiver" type="varchar(255)" remarks="接收人 多个用逗号隔开">
                <constraints nullable="false" />
            </column>
            <column name="ReceiveMode" type="varchar(128)" remarks="接收方式 1短信  2语音 3.邮件 多个用逗号隔开">
                <constraints nullable="false" />
            </column>
            <column name="SendTime" type="time" remarks="发送的具体时间">
                <constraints nullable="true"/>
            </column>
            <column name="SendType" type="int" remarks="发送时间类型 1每天 2每周 3每月">
                <constraints nullable="false"/>
            </column>
            <column name="SendTypeDescription" type="varchar(100)" remarks="每周的星期几与每月的多少号，多个用逗号隔开">
                <constraints nullable="true"/>
            </column>
            <column name="Cron" type="varchar(100)" remarks="corn表达式用于定时发送平安短信">
                <constraints nullable="true"/>
            </column>
            <column name="ContentTemplate" type="varchar(255)" remarks="模板内容">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="配置描述">
                <constraints nullable="true" />
            </column>
            <column name="UsedStatus" type="tinyint(1)" remarks="启用状态">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="SafeMessage" columnName="SafeMessageId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>