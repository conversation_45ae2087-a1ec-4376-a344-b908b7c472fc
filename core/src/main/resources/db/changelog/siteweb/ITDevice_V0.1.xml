<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet id="create_iTDevice_info" author="Liu.Yandong">
        <createTable tableName="ITDevice" remarks="iTDevice info table">
            <column name="ITDeviceId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SerialNumber" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="ITDeviceName" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="ITDeviceModelId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ComputerRackId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UIndex" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Customer" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Business" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="PurchaseDate" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="LaunchDate" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="AssetDeviceId" type="int" remarks="IT设备和资产绑定">
                <constraints nullable="true"/>
            </column>
            <column name="ipaddr" type="varchar(64)" remarks="IP地址">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ITDevice" columnName="ITDeviceId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>


</databaseChangeLog>