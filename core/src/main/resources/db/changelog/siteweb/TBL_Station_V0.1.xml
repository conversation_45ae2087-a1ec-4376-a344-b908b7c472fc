<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblStation_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Station" remarks="tblStation info table">
            <column name="StationId" type="int" remarks="局站ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StationName" type="varchar(255)" remarks="局站名">
                <constraints nullable="false"/>
            </column>
            <column name="Latitude" type="decimal(22,17)" remarks="经度">
                <constraints nullable="true"/>
            </column>
            <column name="Longitude" type="decimal(22,17)" remarks="纬度">
                <constraints nullable="true"/>
            </column>
            <column name="SetupTime" type="datetime" remarks="建站时间">
                <constraints nullable="true"/>
            </column>
            <column name="CompanyId" type="int" remarks="代维公司ID">
                <constraints nullable="true"/>
            </column>
            <column name="ConnectState" type="int" remarks="基站在线状态">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="false"/>
            </column>
            <column name="StationCategory" type="int" remarks="局站类型">
                <constraints nullable="false"/>
            </column>
            <column name="StationGrade" type="int" remarks="局站等级">
                <constraints nullable="false"/>
            </column>
            <column name="StationState" type="int" remarks="局站状态（工程，在建，联网）">
                <constraints nullable="false"/>
            </column>
            <column name="ContactId" type="int"  remarks="联系人ID">
                <constraints nullable="true"/>
            </column>
            <column name="SupportTime" type="int" remarks="电池支撑时间">
                <constraints nullable="true"/>
            </column>
            <column name="OnWayTime" type="double" remarks="油机在途时间">
                <constraints nullable="true"/>
            </column>
            <column name="SurplusTime" type="double" remarks="电池剩余时间">
                <constraints nullable="true"/>
            </column>
            <column name="FloorNo" type="varchar(50)" remarks="楼层">
                <constraints nullable="true"/>
            </column>
            <column name="PropList" type="varchar(255)" remarks="属性列表（来自资管系统）">
                <constraints nullable="true"/>
            </column>
            <column name="Acreage" type="double" remarks="建筑面积">
                <constraints nullable="true"/>
            </column>
            <column name="BuildingType" type="int" remarks="建筑类型">
                <constraints nullable="true"/>
            </column>
            <column name="ContainNode" type="tinyint(1)" remarks="是否包含节点">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="BordNumber" type="int" remarks="负载数">
                <constraints nullable="true"/>
            </column>
            <column name="CenterId" type="int" remarks="所属中心">
                <constraints nullable="false"/>
            </column>
            <column name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="StartTime" type="datetime" remarks="工程开始时间">
                <constraints nullable="true"/>
            </column>
            <column name="EndTime" type="datetime" remarks="工程结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="ProjectName" type="varchar(255)" remarks="工程名">
                <constraints nullable="true"/>
            </column>
            <column name="ContractNo" type="varchar(255)" remarks="合同号">
                <constraints nullable="true"/>
            </column>
            <column name="InstallTime" type="datetime" remarks="安装时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <!--addAutoIncrement tableName="TBL_Station" columnName="StationId" incrementBy="1" columnDataType="int" startWith="1"/-->
    </changeSet>
</databaseChangeLog>