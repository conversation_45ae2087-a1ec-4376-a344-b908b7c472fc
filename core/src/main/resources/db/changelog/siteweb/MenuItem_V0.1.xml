<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_MenuItem_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MenuItem"
                     remarks="MenuItem info table">
            <column name="MenuItemId" type="int" remarks="菜单ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ParentId" type="int" remarks="父菜单ID">
                <constraints nullable="true"/>
            </column>
            <column name="Path" type="varchar(255)" remarks="菜单路径">
                <constraints nullable="true"/>
            </column>
            <column name="Title" type="varchar(255)" remarks="菜单名">
                <constraints nullable="true"/>
            </column>
            <column name="Icon" type="varchar(255)" remarks="菜单图标">
                <constraints nullable="true"/>
            </column>
            <column name="FeatureId" type="int" remarks="所属模块id(对应LicenseFeature表)">
                <constraints nullable="true"/>
            </column>
            <column name="Selected" type="tinyint(1)" remarks="是否默认选中">
                <constraints nullable="true"/>
            </column>
            <column name="Expanded" type="tinyint(1)" remarks="是否可展开">
                <constraints nullable="true"/>
            </column>
            <column name="PathMatch" type="varchar(255)" remarks="路径匹配策略">
                <constraints nullable="true"/>
            </column>
            <column name="LayoutPosition" type="int" remarks="菜单排序">
                <constraints nullable="true"/>
            </column>
            <column name="IsSystemConfig" type="tinyint(1)" remarks="是否系统内置">
                <constraints nullable="true"/>
            </column>
            <column name="IsExternalWeb" type="tinyint(1)" remarks="是否外部URL链接">
                <constraints nullable="true"/>
            </column>
            <column name="MenuHasNavigation" type="tinyint(1)" remarks="是否可跳转">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
            <column name="Alias" type="varchar(255)" remarks="别名">
                <constraints nullable="true"/>
            </column>
            <column name="IsEmbed" type="tinyint(1)" remarks="是否嵌入" defaultValue="0">
                <constraints nullable="true"/>
            </column>
            <column name="SortIndex" type="int">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="MenuItem" columnName="MenuItemId" incrementBy="1" columnDataType="int" startWith="1000"/>
    </changeSet>
</databaseChangeLog>