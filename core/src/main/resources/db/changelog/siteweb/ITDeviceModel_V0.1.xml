<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet id="create_iTDeviceModel_info" author="Liu.Yandong">
        <createTable tableName="ITDeviceModel" remarks="iTDeviceModel info table">
            <column name="ITDeviceModelId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ITDeviceModelName" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="UnitHeight" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Manufactor" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Model" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Brand" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Length" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="Width" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="Height" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RatePower" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RateCooling" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RateWeight" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="ModelFile" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="CategoryId" type="int" remarks="IT设备模型类型(用于机架管理统计)">
                <constraints nullable="true"/>
            </column>
            <column name="AssetDeviceId" type="int" remarks="IT设备和资产绑定">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ITDeviceModel" columnName="ITDeviceModelId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>


</databaseChangeLog>