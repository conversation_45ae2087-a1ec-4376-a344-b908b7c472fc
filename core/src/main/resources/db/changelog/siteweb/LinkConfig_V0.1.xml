<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_linkage_config_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="LinkConfig"
                     remarks="linkage config table">
            <column name="LinkConfigId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ConfigName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="UsedStatus" type="tinyint(1)">
                <constraints nullable="false"/>
            </column>
            <column name="LinkGroupId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="LinkTriggerType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Cron" type="varchar(100)" remarks="corn表达式用于定时执行后台联动">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Layout" type="MEDIUMTEXT">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="LinkConfig" columnName="LinkConfigId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>