ALTER TABLE tbl_activeevent ADD EventReasonType INT NULL COMMENT '告警原因类型';
ALTER TABLE tbl_historyevent ADD EventReasonType INT NULL COMMENT '告警原因类型';
ALTER TABLE tbl_alarmchange ADD EventReasonType INT NULL COMMENT '告警原因类型';

INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(1, '调试告警', '调试告警');
INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(2, '正式告警', '正式告警');
INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(3, '计划操作产生告警', '由于轮询或维修有计划操作产生的告警');
INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(4, '设备故障告警', '设备运行状态下产生的故障性告警');
INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(5, '设备非故障告警', '设备运行状态下发送非故障产生的告警');
INSERT INTO bytedance_eventreasontype (id, name, description) VALUES(6, '通讯异常告警', '设备正常运行，因通讯异常产生的告警（含误告警）');