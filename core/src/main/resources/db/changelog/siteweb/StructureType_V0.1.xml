<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_structuretype_info" author="william" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="StructureType"
                     remarks="StructureType info table">
            <column name="Id" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Name" type="varchar(255)" remarks="类型名">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="photo" type="varchar(255)" remarks="默认照片">
                <constraints nullable="true" unique="false"/>
            </column>
            <column name="color" type="varchar(255)" remarks="颜色">
                <constraints nullable="true" unique="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true" unique="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>