<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_UDevice_info" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_UDevice" remarks="U位管理设备信息表">
            <column name="UDeviceId" type="int" remarks="U位管理设备信息主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="uDeviceNumber" type="varchar(128)" remarks="U位管理设备唯一标识码">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="IsOnline" type="TINYINT(1)" remarks="是否在线">
                <constraints nullable="true" />
            </column>
            <column name="RackId" type="int" remarks="对应机架Id">
                <constraints nullable="true" unique="true"/>
            </column>
            <column name="ModuleCnt" type="int" remarks="额定U位">
                <constraints nullable="true" />
            </column>
            <column name="UTagCnt" type="int" remarks="标签数">
                <constraints nullable="true" />
            </column>
            <column name="IpAddr" type="varchar(45)" remarks="IP地址">
                <constraints nullable="true" />
            </column>
            <column name="SWEquipmentId" type="int" remarks="SiteWeb设备Id(U位管理与SW采集设备关联关系)">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_UDevice" columnName="UDeviceId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>