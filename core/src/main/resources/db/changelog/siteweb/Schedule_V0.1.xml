<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_Schedule_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="Schedule" remarks="Schedule info table">
            <column name="ScheduleId" type="int" remarks="排班主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ShiftGroupMapId" type="int" remarks="人员班次映射id">
                <constraints nullable="false"/>
            </column>
            <column name="ShiftId" type="int" remarks="班次id">
                <constraints nullable="false"/>
            </column>
            <column name="ScheduleTime" type="date" remarks="排版日期">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Schedule" columnName="ScheduleId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>