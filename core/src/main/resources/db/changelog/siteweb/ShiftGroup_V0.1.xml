<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_ShiftGroup_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ShiftGroup" remarks="ShiftGroup info table">
            <column name="ShiftGroupId" type="int" remarks="班组主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ShiftGroupName" type="varchar(50)" remarks="班组名称">
                <constraints nullable="false"/>
            </column>
            <column name="Remark" type="varchar(255)" remarks="备注">
                <constraints nullable="true"/>
            </column>
            <column name="DepartmentId" type="int" remarks="部门id" defaultValue="0">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ShiftGroup" columnName="ShiftGroupId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>