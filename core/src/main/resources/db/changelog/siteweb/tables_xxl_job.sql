-- #
-- # XXL-JOB v2.4.1-SNAPSHOT
-- # Copyright (c) 2015-present, xuxueli.

-- # 表结构
CREATE TABLE `xxl_job_info` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
                                `job_desc` varchar(255) NOT NULL,
                                `add_time` datetime DEFAULT NULL,
                                `update_time` datetime DEFAULT NULL,
                                `author` varchar(64) DEFAULT NULL COMMENT '作者',
                                `alarm_email` varchar(255) DEFAULT NULL COMMENT '报警邮件',
                                `schedule_type` varchar(50) NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
                                `schedule_conf` varchar(128) DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
                                `misfire_strategy` varchar(50) NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
                                `executor_route_strategy` varchar(50) DEFAULT NULL COMMENT '执行器路由策略',
                                `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
                                `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
                                `executor_block_strategy` varchar(50) DEFAULT NULL COMMENT '阻塞处理策略',
                                `executor_timeout` int(11) NOT NULL DEFAULT '0' COMMENT '任务执行超时时间，单位秒',
                                `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
                                `glue_type` varchar(50) NOT NULL COMMENT 'GLUE类型',
                                `glue_source` mediumtext COMMENT 'GLUE源代码',
                                `glue_remark` varchar(128) DEFAULT NULL COMMENT 'GLUE备注',
                                `glue_updatetime` datetime DEFAULT NULL COMMENT 'GLUE更新时间',
                                `child_jobid` varchar(255) DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
                                `trigger_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '调度状态：0-停止，1-运行',
                                `trigger_last_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '上次调度时间',
                                `trigger_next_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '下次调度时间',
                                PRIMARY KEY (`id`)
);

CREATE TABLE `xxl_job_log` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT,
                               `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
                               `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
                               `executor_address` varchar(255) DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
                               `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
                               `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
                               `executor_sharding_param` varchar(20) DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
                               `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
                               `trigger_time` datetime DEFAULT NULL COMMENT '调度-时间',
                               `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
                               `trigger_msg` text COMMENT '调度-日志',
                               `handle_time` datetime DEFAULT NULL COMMENT '执行-时间',
                               `handle_code` int(11) NOT NULL COMMENT '执行-状态',
                               `handle_msg` text COMMENT '执行-日志',
                               `alarm_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
                               PRIMARY KEY (`id`),
                               KEY `I_trigger_time` (`trigger_time`),
                               KEY `I_handle_code` (`handle_code`)
);

CREATE TABLE `xxl_job_log_report` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `trigger_day` datetime DEFAULT NULL COMMENT '调度-时间',
                                      `running_count` int(11) NOT NULL DEFAULT '0' COMMENT '运行中-日志数量',
                                      `suc_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行成功-日志数量',
                                      `fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行失败-日志数量',
                                      `update_time` datetime DEFAULT NULL,
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `i_trigger_day` (`trigger_day`) USING BTREE
);

CREATE TABLE `xxl_job_logglue` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
                                   `glue_type` varchar(50) DEFAULT NULL COMMENT 'GLUE类型',
                                   `glue_source` mediumtext COMMENT 'GLUE源代码',
                                   `glue_remark` varchar(128) NOT NULL COMMENT 'GLUE备注',
                                   `add_time` datetime DEFAULT NULL,
                                   `update_time` datetime DEFAULT NULL,
                                   PRIMARY KEY (`id`)
);

CREATE TABLE `xxl_job_registry` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `registry_group` varchar(50) NOT NULL,
                                    `registry_key` varchar(255) NOT NULL,
                                    `registry_value` varchar(255) NOT NULL,
                                    `update_time` datetime DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `i_g_k_v` (`registry_group`,`registry_key`,`registry_value`)
);

CREATE TABLE `xxl_job_group` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `app_name` varchar(64) NOT NULL COMMENT '执行器AppName',
                                 `title` varchar(64) NOT NULL COMMENT '执行器名称',
                                 `address_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '执行器地址类型：0=自动注册、1=手动录入',
                                 `address_list` text COMMENT '执行器地址列表，多地址逗号分隔',
                                 `update_time` datetime DEFAULT NULL,
                                 PRIMARY KEY (`id`)
);

CREATE TABLE `xxl_job_user` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `username` varchar(50) NOT NULL COMMENT '账号',
                                `password` varchar(50) NOT NULL COMMENT '密码',
                                `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
                                `permission` varchar(255) DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `i_username` (`username`) USING BTREE
);

CREATE TABLE `xxl_job_lock` (
                                `lock_name` varchar(50) NOT NULL COMMENT '锁名称',
                                PRIMARY KEY (`lock_name`)
);

-- # 初始化语句
INSERT INTO `xxl_job_group`(`id`, `app_name`, `title`, `address_type`, `address_list`, `update_time`) VALUES (1, 'siteweb-job-executor', 'JobServer执行器', 0, NULL, '2024-04-26 09:34:56' );

INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, 'WorkStation自诊断', '2024-04-26 09:34:56', '2024-04-26 09:34:56', 'siteweb', '', 'CRON', '0/20 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'workStationDiagnosesJobHandler', '240', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-04-26 09:34:56', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, 'MU自诊断', '2024-05-09 10:41:31', '2024-05-09 10:41:31', 'siteweb', '', 'CRON', '0/30 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'muDiagnosesJobHandler', '300', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-09 10:41:31', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理日志数据', '2024-06-04 10:11:53', '2024-06-04 13:42:17', 'liaoximing', '', 'CRON', '0 30 1 * * ?', 'DO_NOTHING', 'FIRST', 'clearLogJobHandler', '180;200000', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-09-20 10:21:31', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '记录机架容量', '2024-06-04 15:57:21', '2024-06-04 16:28:00', 'liaoximing', '', 'CRON', '0 0 0 1 * ?', 'DO_NOTHING', 'FIRST', 'rackCapacityRecordJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-04 15:57:21', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '每小时锁定一次不活跃用户', '2024-06-06 10:14:03', '2024-06-06 10:14:24', 'liaoximing', '', 'CRON', '0 0 * * * ?', 'DO_NOTHING', 'FIRST', 'lockInactiveUsersJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-06 10:14:03', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '配置同步(检查)', '2024-05-24 11:30:31', '2024-05-24 11:30:31', 'siteweb', '', 'CRON', '0 0 20 * * ?', 'DO_NOTHING', 'FAILOVER', 'monitorUnitSyncCheckJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-24 11:30:31', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '配置同步(下发)', '2024-05-25 13:46:12', '2024-05-25 13:46:12', 'siteweb', '', 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FAILOVER', 'monitorUnitSyncExecJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-25 13:46:12', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理过期控制', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FAILOVER', 'clearTimeoutControlJobHandler', '180,86400', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理没有配置的活动事件', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FAILOVER', 'clearActiveEventJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '定时向workstation表报到并生成工作站告警', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0/30 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'bSDiagnosesJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
# INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '分发门禁控制命令', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '3', 'DO_NOTHING', 'FAILOVER', 'distributeControlOfDoorJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '自动确认告警', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '300', 'DO_NOTHING', 'FAILOVER', 'autoConfirmActiveEventJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理告警变化', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '1800', 'DO_NOTHING', 'FAILOVER', 'clearAlarmChangeJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理屏蔽事件', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '300', 'DO_NOTHING', 'FAILOVER', 'clearEventMaskJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史告警记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryEventJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史告警屏蔽记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryEventMaskJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史控制记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryControlJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史人员登录记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearLoginInfoJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史刷卡记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearSwapCardRecordJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史操作记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearOperationRecordJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '每分钟更新设备工程态状态', '2024-07-10 14:30:05', '2024-07-10 14:30:30', 'liaoximing', '', 'CRON', '0 * * * * ?', 'DO_NOTHING', 'FIRST', 'equipmentMaintainJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-07-10 14:30:05', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '同步FSU运行信息', '2024-11-26 14:30:05', '2024-11-26 14:30:30', 'liaoximing', '', 'CRON', '0 0 6 * * ?', 'DO_NOTHING', 'FIRST', 'fsuDataInfoSync', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-26 14:30:05', '', 0);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '记录机架信号开通率和功率', '2025-02-28 15:57:21', '2025-02-28 16:28:00', 'shenhaijun', '', 'CRON', '0 0 * * * ?', 'DO_NOTHING', 'FIRST', 'computerRackSignalRecordJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-02-28 15:57:21', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '清理历史预警及预警变化表记录', '2025-03-19 16:38:22', '2025-03-19 16:38:22', 'siteweb', '', 'CRON', '0 0 4 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearPreAlarmHistoryJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-19 16:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '密码到期短信提醒', '2025-03-21 10:38:22', '2025-03-21 10:38:22', 'liaoximing', '', 'CRON', '0 0 9 * * ?', 'DO_NOTHING', 'FAILOVER', 'passwordExpirationSmsReminderJob', '动环监控平台提醒您：请尽快修改密码，密码到期帐户将被锁定', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-21 10:38:22', '', 1);
INSERT INTO `xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`) VALUES (1, '每分钟更新局站工程态状态', '2025-06-30 10:38:22', '2025-06-30 10:38:22', 'liaoximing', '', 'CRON', '0 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'stationMaintainJob', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-06-30 10:38:22', '', 1);


INSERT INTO `xxl_job_user`(`id`, `username`, `password`, `role`, `permission`) VALUES (1, 'admin', 'f6fdffe48c908deb0f4c3bd36c032e72', 1, NULL); -- 默认密码：adminadmin

INSERT INTO `xxl_job_lock` ( `lock_name`) VALUES ( 'schedule_lock');

commit;
