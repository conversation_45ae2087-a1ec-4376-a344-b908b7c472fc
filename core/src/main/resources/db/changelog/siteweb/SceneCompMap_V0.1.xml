<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_scenecompmap_info" author="psx">
        <createTable tableName="scenecompmap" remarks="scenecompmap info table">
            <column name="SceneCompMapId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="type" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="SceneId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="PageCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CompType" type="int">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="scenecompmap" columnName="SceneCompMapId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>