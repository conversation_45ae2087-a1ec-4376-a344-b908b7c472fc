<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="DoorTag" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DoorTag" remarks="门标签表">
            <column name="TagId" type="int" remarks="主键自增id">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TagName" type="varchar(128)" remarks="标签名称">
                <constraints nullable="false"/>
            </column>
            <column name="TagIcon" type="varchar(128)" remarks="标签图标">
                <constraints nullable="false"/>
            </column>
            <column name="TagColor" type="varchar(128)" remarks="标签颜色">
                <constraints nullable="true"/>
            </column>
            <column name="TagDescribe" type="varchar(128)" remarks="标签描述">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="DoorTag" columnName="TagId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>