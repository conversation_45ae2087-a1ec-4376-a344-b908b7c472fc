<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_baseUnit_info" author="wxc">
        <createTable tableName="BaseUnit">
            <column name="baseUnitId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseUnitName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseUnitNameEn" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseUnitSymbol" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="BaseUnitNameCode" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseUnitSymbolName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseUnitNameDescription" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="BaseUnit" columnName="BaseUnitId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>