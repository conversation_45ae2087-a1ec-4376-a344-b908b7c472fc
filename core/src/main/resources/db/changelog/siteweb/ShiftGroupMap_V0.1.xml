<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_ShiftGroupMap_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ShiftGroupMap" remarks="ShiftGroupMap info table">
            <column name="ShiftGroupMapId" type="int" remarks="人员班组映射主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ShiftGroupId" type="int" remarks="班组id">
                <constraints nullable="false"/>
            </column>
            <column name="EmployeeId" type="int" remarks="员工id">
                <constraints nullable="false"/>
            </column>
            <column name="DisplayIndex" type="int" remarks="展示顺序">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ShiftGroupMap" columnName="ShiftGroupMapId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>