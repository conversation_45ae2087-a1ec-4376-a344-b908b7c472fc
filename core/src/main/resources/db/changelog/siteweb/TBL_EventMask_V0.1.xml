<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet id="create_tblEventMask_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EventMask" remarks="EventMask info table">
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="INT" remarks="基站ID">
                <constraints nullable="false" />
            </column>
            <column name="EventId" type="INT" remarks="告警ID">
                <constraints nullable="false"/>
            </column>
            <column name="TimeGroupId" type="INT" remarks="时间分组ID"/>
            <column name="Reason" type="VARCHAR(255)" remarks="屏蔽原因"/>
            <column name="StartTime" type="datetime" remarks="屏蔽开始时间"/>
            <column name="EndTime" type="datetime" remarks="屏蔽结束时间"/>
            <column name="UserId" type="INT" remarks="屏蔽人ID"/>
        </createTable>
        <createIndex tableName="TBL_EventMask" indexName="IDX_EventMASK_COMPLEX1" unique="true">
            <column name="StationId"></column>
            <column name="EquipmentId"></column>
            <column name="EventId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>