<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="CommonObject" author="liaoximing" >
        <createTable tableName="CommonObject" remarks="通用对象信息表">
            <column name="CommonObjectId" type="int" remarks="通用对象id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CommonObjectName" type="varchar(255)" remarks="通用对象名称">
                <constraints nullable="false" />
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true" />
            </column>
            <column name="Photo" type="varchar(255)" remarks="图片">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="CommonObject" columnName="CommonObjectId" incrementBy="1"  columnDataType="int" startWith="10000" />
    </changeSet>
</databaseChangeLog>