<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="DimensionLinks" author="shenhaijun" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="dimensionlinks" remarks="Dimension links table">
            <column name="LineId" type="varchar(64)" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DisplayName" type="varchar(64)" remarks="线路显示名称">
                <constraints nullable="true"/>
            </column>
            <column name="LineType" type="int" remarks="线路类型">
                <constraints nullable="true"/>
            </column>
            <column name="LocalObjectId" type="int" remarks="本端对象ID">
                <constraints nullable="true"/>
            </column>
            <column name="LocalObjectType" type="int" remarks="本端对象类型">
                <constraints nullable="true"/>
            </column>
            <column name="LocalObjectPort" type="varchar(64)" remarks="本端对象端口">
                <constraints nullable="true"/>
            </column>
            <column name="LocalObjectModelPort" type="int" remarks="本端对象3D模型端口">
                <constraints nullable="true"/>
            </column>
            <column name="RemoteObjectId" type="int" remarks="远端对象id">
                <constraints nullable="true"/>
            </column>
            <column name="RemoteObjectType" type="int" remarks="远端对象类型">
                <constraints nullable="true"/>
            </column>
            <column name="RemoteObjectPort" type="varchar(64)" remarks="远端对象端口">
                <constraints nullable="true"/>
            </column>
            <column name="RemoteObjectModelPort" type="int" remarks="远端对象3D模型端口">
                <constraints nullable="true"/>
            </column>
            <column name="StaticPropertys" type="json" remarks="静态属性">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(128)" remarks="备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>