<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_assetdevice_info" author="lzy">
        <createTable tableName="AssetDevice" remarks="资产表">
            <column name="AssetDeviceId" type="int" remarks="资产id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SortIndex" type="int" remarks="排序">
                <constraints nullable="true" />
            </column>
            <column name="AssetCode" type="varchar(128)" remarks="资产编号">
                <constraints nullable="true" />
            </column>
            <column name="AssetName" type="varchar(128)" remarks="资产名称">
                <constraints nullable="true" />
            </column>
            <column name="AssetCategoryId" type="int" remarks="资产类型">
                <constraints nullable="true" />
            </column>
            <column name="Brand" type="varchar(128)" remarks="品牌">
                <constraints nullable="true" />
            </column>
            <column name="Model" type="varchar(128)" remarks="型号">
                <constraints nullable="true" />
            </column>
            <column name="CapacityParameter" type="varchar(128)" remarks="容量参数">
                <constraints nullable="true" />
            </column>
            <column name="SettingPosition" type="varchar(128)" remarks="安装位置">
                <constraints nullable="true" />
            </column>
            <column name="SerialNumber" type="varchar(128)" remarks="序列号">
                <constraints nullable="true" />
            </column>
            <column name="Manufactor" type="varchar(128)" remarks="厂家">
                <constraints nullable="true" />
            </column>
            <column name="TableName" type="varchar(128)" defaultValue="assetdevice" remarks="关联表">
                <constraints nullable="true" />
            </column>
<!--            <column name="ObjectId" type="int">-->
<!--                <constraints nullable="true"/>-->
<!--            </column>-->
<!--            <column name="ObjectType" type="int">-->
<!--                <constraints nullable="true" />-->
<!--            </column>-->
<!--            <column name="GlobalResourceId" type="bigint">-->
<!--                <constraints nullable="true" />-->
<!--            </column>-->
        </createTable>
        <addAutoIncrement tableName="AssetDevice" columnName="AssetDeviceId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>