<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_permission_info" author="wiliam" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="Permission"
                     remarks="basic Permission info table">
            <column name="PermissionId" type="int" remarks="权限ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Name" type="varchar(128)" remarks="权限名称">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="Category" type="int" remarks="权限分类">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="Caption" type="varchar(255)" remarks="标题">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Permission" columnName="PermissionId" incrementBy="1" columnDataType="int"
                          startWith="1000"/>
    </changeSet>
</databaseChangeLog>