<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportSchema_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportSchema" remarks="ReportSchema info table">
            <column name="ReportSchemaId" type="int" remarks="报表SchemaID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportSchemaName" type="varchar(255)" remarks="报表Schema名称">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaDescription" type="varchar(255)" remarks="报表Schema备注">
                <constraints nullable="true"/>
            </column>
            <column name="Version" type="varchar(32)" remarks="版本">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaCategoryId" type="int" remarks="报表Schema类型ID">
                <constraints nullable="true"/>
            </column>
            <column name="Author" type="varchar(128)" remarks="创建人">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
            <column name="ReportDataSourceId" type="int" remarks="报表数据源ID">
                <constraints nullable="false"/>
            </column>
            <column name="ViewControlId" type="int" remarks="是否隐藏">
                <constraints nullable="false"/>
            </column>
            <column name="MaxQueryInterval" type="int" remarks="最大查询间隔">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportSchema" columnName="ReportSchemaId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>