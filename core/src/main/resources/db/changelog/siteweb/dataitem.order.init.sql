-- 排序1-50 用于优先级1的端口
-- 标准串口
UPDATE tbl_dataitem SET ExtendField1 = '1' WHERE EntryId = 39 AND ItemId = 1;
-- 虚拟端口
UPDATE tbl_dataitem SET ExtendField1 = '2' WHERE EntryId = 39 AND ItemId = 5;
-- S<PERSON><PERSON>口
UPDATE tbl_dataitem SET ExtendField1 = '3' WHERE EntryId = 39 AND ItemId = 3;
-- SNMP端口(Linux RMU)
UPDATE tbl_dataitem SET ExtendField1 = '4' WHERE EntryId = 39 AND ItemId = 33;
-- 简单逻辑控制口
UPDATE tbl_dataitem SET ExtendField1 = '5' WHERE EntryId = 39 AND ItemId = 19;
-- 移动B接口门禁透传端口
UPDATE tbl_dataitem SET ExtendField1 = '6' WHERE EntryId = 39 AND ItemId = 31;
-- 终端服务器口
UPDATE tbl_dataitem SET ExtendField1 = '20' WHERE EntryId = 39 AND ItemId = 6;

-- 排序50-100 用于优先级2的端口
UPDATE tbl_dataitem SET ExtendField1 = '52' WHERE EntryId = 39 AND ItemId = 2;
UPDATE tbl_dataitem SET ExtendField1 = '53' WHERE EntryId = 39 AND ItemId = 4;
UPDATE tbl_dataitem SET ExtendField1 = '54' WHERE EntryId = 39 AND ItemId = 7;
UPDATE tbl_dataitem SET ExtendField1 = '55' WHERE EntryId = 39 AND ItemId = 8;
UPDATE tbl_dataitem SET ExtendField1 = '56' WHERE EntryId = 39 AND ItemId = 9;
UPDATE tbl_dataitem SET ExtendField1 = '57' WHERE EntryId = 39 AND ItemId = 10;
UPDATE tbl_dataitem SET ExtendField1 = '58' WHERE EntryId = 39 AND ItemId = 11;
UPDATE tbl_dataitem SET ExtendField1 = '59' WHERE EntryId = 39 AND ItemId = 12;
UPDATE tbl_dataitem SET ExtendField1 = '60' WHERE EntryId = 39 AND ItemId = 13;
UPDATE tbl_dataitem SET ExtendField1 = '61' WHERE EntryId = 39 AND ItemId = 14;
UPDATE tbl_dataitem SET ExtendField1 = '62' WHERE EntryId = 39 AND ItemId = 15;
UPDATE tbl_dataitem SET ExtendField1 = '63' WHERE EntryId = 39 AND ItemId = 16;
UPDATE tbl_dataitem SET ExtendField1 = '64' WHERE EntryId = 39 AND ItemId = 17;
UPDATE tbl_dataitem SET ExtendField1 = '65' WHERE EntryId = 39 AND ItemId = 18;
UPDATE tbl_dataitem SET ExtendField1 = '66' WHERE EntryId = 39 AND ItemId = 20;
UPDATE tbl_dataitem SET ExtendField1 = '67' WHERE EntryId = 39 AND ItemId = 21;
UPDATE tbl_dataitem SET ExtendField1 = '68' WHERE EntryId = 39 AND ItemId = 22;
UPDATE tbl_dataitem SET ExtendField1 = '69' WHERE EntryId = 39 AND ItemId = 23;
UPDATE tbl_dataitem SET ExtendField1 = '70' WHERE EntryId = 39 AND ItemId = 24;
UPDATE tbl_dataitem SET ExtendField1 = '71' WHERE EntryId = 39 AND ItemId = 25;
UPDATE tbl_dataitem SET ExtendField1 = '72' WHERE EntryId = 39 AND ItemId = 26;
UPDATE tbl_dataitem SET ExtendField1 = '73' WHERE EntryId = 39 AND ItemId = 27;
UPDATE tbl_dataitem SET ExtendField1 = '74' WHERE EntryId = 39 AND ItemId = 32;
UPDATE tbl_dataitem SET ExtendField1 = '75' WHERE EntryId = 39 AND ItemId = 28;
UPDATE tbl_dataitem SET ExtendField1 = '76' WHERE EntryId = 39 AND ItemId = 29;
UPDATE tbl_dataitem SET ExtendField1 = '7' WHERE EntryId = 39 AND ItemId = 30;



-- 设置监控单元类型排序
UPDATE tbl_dataitem SET ExtendField2 = '4' WHERE EntryId = 34 AND ItemId = 1;
UPDATE tbl_dataitem SET ExtendField2 = '11' WHERE EntryId = 34 AND ItemId = 2;
UPDATE tbl_dataitem SET ExtendField2 = '14' WHERE EntryId = 34 AND ItemId = 6;
UPDATE tbl_dataitem SET ExtendField2 = '13' WHERE EntryId = 34 AND ItemId = 9;
UPDATE tbl_dataitem SET ExtendField2 = '15' WHERE EntryId = 34 AND ItemId = 10;
UPDATE tbl_dataitem SET ExtendField2 = '16' WHERE EntryId = 34 AND ItemId = 11;
UPDATE tbl_dataitem SET ExtendField2 = '1' WHERE EntryId = 34 AND ItemId = 12;
UPDATE tbl_dataitem SET ExtendField2 = '3' WHERE EntryId = 34 AND ItemId = 14;
UPDATE tbl_dataitem SET ExtendField2 = '5' WHERE EntryId = 34 AND ItemId = 16;
UPDATE tbl_dataitem SET ExtendField2 = '6' WHERE EntryId = 34 AND ItemId = 17;




