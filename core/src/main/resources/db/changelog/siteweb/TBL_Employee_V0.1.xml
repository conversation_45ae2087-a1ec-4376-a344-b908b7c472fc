<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblEmployee_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Employee" remarks="tblEmployee info table">
            <column name="EmployeeId" type="int" remarks="雇员ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DepartmentId" type="int" remarks="部门ID">
                <constraints nullable="true"/>
            </column>
            <column name="EmployeeName" type="varchar(128)" remarks="雇员名">
                <constraints nullable="false"/>
            </column>
            <column name="EmployeeType" type="int" remarks="雇员分类">
                <constraints nullable="true"/>
            </column>
            <column name="EmployeeTitle" type="int" remarks="雇员头衔">
                <constraints nullable="true"/>
            </column>
            <column name="JobNumber" type="varchar(20)" remarks="雇员工号">
                <constraints nullable="false"/>
            </column>
            <column name="Gender" type="int" remarks="性别">
                <constraints nullable="true"/>
            </column>
            <column name="Mobile" type="varchar(50)" remarks="手机号码">
                <constraints nullable="true"/>
            </column>
            <column name="Phone" type="varchar(50)" remarks="座机号码">
                <constraints nullable="true"/>
            </column>
            <column name="Email" type="varchar(128)" remarks="邮件地址">
                <constraints nullable="true"/>
            </column>
            <column name="Address" type="varchar(255)" remarks="住址">
                <constraints nullable="true"/>
            </column>
            <column name="PostAddress" type="varchar(255)" remarks="邮政地址">
                <constraints nullable="true"/>
            </column>
            <column defaultValue="1" name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column defaultValue="0" name="IsAddTempUser" type="tinyint(1)" remarks="是否临时用户">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="172800" name="UserValidTime" type="int" remarks="有效时长">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>