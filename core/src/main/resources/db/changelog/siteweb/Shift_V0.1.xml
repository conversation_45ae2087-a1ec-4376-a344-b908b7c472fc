<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_Shift_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="Shift" remarks="Shift info table">
            <column name="ShiftId" type="int" remarks="班次主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ShiftName" type="varchar(50)" remarks="班次名称">
                <constraints nullable="false"/>
            </column>
            <column name="ShiftStartTime" type="time" remarks="班次开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="ShiftEndTime" type="time" remarks="班次结束时间">
                <constraints nullable="false"/>
            </column>
            <column name="ShiftColor" type="varchar(50)" remarks="班次颜色">
                <constraints nullable="false"/>
            </column>
            <column name="DepartmentId" type="int" remarks="部门Id" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Shift" columnName="ShiftId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>