<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_messagestatus_info" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MessageStatus" remarks="basic MessageStatus  info table">
            <column name="MessageStatusId" type="int" remarks="ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="InternalMessageId" type="int" remarks="站内信消息id">
                <constraints nullable="false" />
            </column>
            <column name="UserId" type="int" remarks="接收用户id">
                <constraints nullable="false"/>
            </column>
            <column name="MessageStatus" type="int" remarks="消息状态 0未读  1已读">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="MessageStatus" columnName="MessageStatusId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>