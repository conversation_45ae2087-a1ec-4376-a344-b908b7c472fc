<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_reportfolder_table" author="shj" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="reportfolder" remarks="报表文件夹信息">
            <column name="folderId" type="int" remarks="文件夹ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="folderName" type="varchar(255)" remarks="文件夹名称">
                <constraints nullable="false"/>
            </column>
            <column name="parentId" type="int" remarks="父文件夹ID，根节点为 0" defaultValueNumeric="0">
                <constraints nullable="true"/>
            </column>
            <column name="sortIndex" type="int" remarks="排序值">
                <constraints nullable="true"/>
            </column>
            <column name="createTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="reportfolder" columnName="folderId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>