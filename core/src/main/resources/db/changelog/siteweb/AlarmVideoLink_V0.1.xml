<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AlarmVideoLink" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmVideoLink" remarks="AlarmVideoLink info table">
            <column name="AlarmVideoLinkId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ConfigName" type="varchar(255)" remarks="实列名称">
                <constraints nullable="false" />
            </column>
            <column name="Description" type="varchar(255)" remarks="实列描述">
                <constraints nullable="true" />
            </column>
            <column name="UsedStatus" type="tinyint(1)" remarks="启用状态">
                <constraints nullable="false" />
            </column>
            <column name="DepartmentId" type="int" remarks="部门id" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="LinkType" type="varchar(16)" remarks="联动类型">
                <constraints nullable="true" />
            </column>
            <column name="OperationType" type="varchar(16)" remarks="事件状态">
                <constraints nullable="true" />
            </column>
            <column name="SnapshotCount" type="int" remarks="抓图张数">
                <constraints nullable="true" />
            </column>
            <column name="SnapshotInterval" type="int" remarks="抓图间隔（秒）">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmVideoLink" columnName="AlarmVideoLinkId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>