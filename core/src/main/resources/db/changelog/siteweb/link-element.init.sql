INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (1,'整数加法','整数运算',1,1,null,'+',true,1,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (2,'整数减法','整数运算',2,1,null,'-',true,2,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (3,'整数乘法','整数运算',1,1,null,'*',true,3,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (4,'整数除法','整数运算',2,1,null,'/',true,4,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (5,'整数取模','整数运算',2,1,null,'%',true,5,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (6,'浮点数加法','浮点数运算',1,1,null,'+',true,6,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (7,'浮点数减法','浮点数运算',2,1,null,'-',true,7,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (8,'浮点数乘法','浮点数运算',1,1,null,'*',true,8,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (9,'浮点数除法','浮点数运算',2,1,null,'/',true,9,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (10,'>','关系运算',2,1,null,'>',true,10,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (11,'>=','关系运算',2,1,null,'>=',true,11,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (12,'=','关系运算',2,1,null,'==',true,12,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (13,'<=','关系运算',2,1,null,'<=',true,13,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (14,'<','关系运算',2,1,null,'<',true,14,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (15,'!=','关系运算',2,1,null,'!=',true,15,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (16,'逻辑与','逻辑运算',1,1,null,'&&',true,16,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (17,'逻辑或','逻辑运算',1,1,null,'||',true,17,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (18,'逻辑非','逻辑运算',1,1,null,'!',true,18,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (19,'最大值','聚合函数',1,1,null,'max()',true,19,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (20,'最小值','聚合函数',1,1,null,'min()',true,20,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (21,'平均值','聚合函数',1,1,null,'mean()',true,21,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (31,'事件开始','事件',1,1,null,null,true,31,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (32,'事件结束','事件',1,1,null,null,true,32,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (33,'当前时间','时间组件',1,1,null,null,true,33,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (34,'时间延迟','时间组件',1,1,null,null,true,34,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (35,'事件确认','事件',1,1,null,null,true,35,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (36,'事件存在','事件',1,1,null,null,true,36,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (41,'并行执行','联动控制',1,1,null,null,true,41,null);
-- INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
-- VALUES (42,'执行脚本','联动控制',1,1,null,null,true,42,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (43,'空指令','联动控制',1,0,null,null,true,43,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (44,'设备控制','联动控制',1,1,null,null,true,44,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (51,'条件运算','条件运算',1,2,null,'if()',true,51,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (52,'整数常量','常量',1,1,null,null,true,52,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (53,'浮点数常量','常量',1,1,null,null,true,53,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (61,'测点','测点',1,1,null,null,false,61,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (62,'设备','设备',0,1,null,null,false,62,null);
INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (63,'层级','层级',0,1,null,null,false,63,null);

