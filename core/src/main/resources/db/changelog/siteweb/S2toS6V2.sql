DROP TABLE IF EXISTS account;
DROP TABLE IF EXISTS accountmenuprofilemap;
DROP TABLE IF EXISTS accountrefdrawdesigns;
DROP TABLE IF EXISTS accountrolemap;
DROP TABLE IF EXISTS aiairconditionerdevicemap;
DROP TABLE IF EXISTS aicommand;
DROP TABLE IF EXISTS aigroup;
DROP TABLE IF EXISTS aioverheatingarea;
DROP TABLE IF EXISTS aioverheatingareadevicemap;
DROP TABLE IF EXISTS aioverheatingrecord;
DROP TABLE IF EXISTS airconditionergroup;
DROP TABLE IF EXISTS airconditionershadow;
DROP TABLE IF EXISTS alarmautoconfirmconf;
DROP TABLE IF EXISTS alarmmasklog;
DROP TABLE IF EXISTS alarmnotifyconfig;
DROP TABLE IF EXISTS alarmnotifyelement;
DROP TABLE IF EXISTS alarmnotifyelementconfig;
DROP TABLE IF EXISTS alarmnotifyfiltercondition;
DROP TABLE IF EXISTS alarmnotifyfilter<PERSON>le;
DROP TABLE IF EXISTS alarmnotifynode;
DROP TABLE IF EXISTS alarmnotifyrecord;
DROP TABLE IF EXISTS alarmnotifysegment;
DROP TABLE IF EXISTS announcement;
DROP TABLE IF EXISTS approvalinstance;
DROP TABLE IF EXISTS approvalinstancecomment;
DROP TABLE IF EXISTS approvalinstancefilemap;
DROP TABLE IF EXISTS approvalinstancelog;
DROP TABLE IF EXISTS approvalinstanceparticipantmap;
DROP TABLE IF EXISTS approvalinstancetagmap;
DROP TABLE IF EXISTS approvalprocess;
DROP TABLE IF EXISTS approvalstep;
DROP TABLE IF EXISTS assetcategory;
DROP TABLE IF EXISTS assetdevice;
DROP TABLE IF EXISTS assetentitydutymap;
DROP TABLE IF EXISTS autoinspecttask;
DROP TABLE IF EXISTS autoinspecttaskresult;
DROP TABLE IF EXISTS autoinspecttimesetting;
DROP TABLE IF EXISTS backlog;
DROP TABLE IF EXISTS backlogcandidateusermap;
DROP TABLE IF EXISTS basepointdictionary;
DROP TABLE IF EXISTS basepointlogiccategory;
DROP TABLE IF EXISTS baseunit;
DROP TABLE IF EXISTS batterycellmodel;
DROP TABLE IF EXISTS batterycellpoint;
DROP TABLE IF EXISTS batterydevicebackuptime;
DROP TABLE IF EXISTS batterydevicemapping;
DROP TABLE IF EXISTS batterydischargerecord;
DROP TABLE IF EXISTS batterystring;
DROP TABLE IF EXISTS batterystringpoint;
DROP TABLE IF EXISTS callingcard;
DROP TABLE IF EXISTS camera;
DROP TABLE IF EXISTS cameragroup;
DROP TABLE IF EXISTS camerapollgroup;
DROP TABLE IF EXISTS camerapollgroupmap;
DROP TABLE IF EXISTS capacityattribute;
DROP TABLE IF EXISTS capacitybaseattribute;
DROP TABLE IF EXISTS capacitybasetype;
DROP TABLE IF EXISTS center;
DROP TABLE IF EXISTS chatmessage;
DROP TABLE IF EXISTS chatmessagereceivermap;
DROP TABLE IF EXISTS chatsession;
DROP TABLE IF EXISTS chatsessionuser;
DROP TABLE IF EXISTS checktaskassignment;
DROP TABLE IF EXISTS cmdbcategory;
DROP TABLE IF EXISTS cmdbentryconfig;
DROP TABLE IF EXISTS collectengine;
DROP TABLE IF EXISTS columncabinetrefcomputerrack;
DROP TABLE IF EXISTS commandstate;
DROP TABLE IF EXISTS commonobject;
DROP TABLE IF EXISTS compdatacolumn;
DROP TABLE IF EXISTS compdataset;
DROP TABLE IF EXISTS compdatatable;
DROP TABLE IF EXISTS complaintticket;
DROP TABLE IF EXISTS complexindex;
DROP TABLE IF EXISTS complexindexbusinessobjectmap;
DROP TABLE IF EXISTS complexindexbusinesstype;
DROP TABLE IF EXISTS complexindexdefinition;
DROP TABLE IF EXISTS complexindexdefinitionrelation;
DROP TABLE IF EXISTS complexindexfunction;
DROP TABLE IF EXISTS complexindextemplate;
DROP TABLE IF EXISTS compskin;
DROP TABLE IF EXISTS computerrack;
DROP TABLE IF EXISTS contract;
DROP TABLE IF EXISTS contractterm;
DROP TABLE IF EXISTS convergenceevent;
DROP TABLE IF EXISTS coredatatype;
DROP TABLE IF EXISTS coreeventseverity;
DROP TABLE IF EXISTS coreeventtype;
DROP TABLE IF EXISTS corepoint;
DROP TABLE IF EXISTS corepointmeaning;
DROP TABLE IF EXISTS corepointrefproperty;
DROP TABLE IF EXISTS corepointsitewebgatewaymap;
DROP TABLE IF EXISTS corepointtaskmap;
DROP TABLE IF EXISTS coresource;
DROP TABLE IF EXISTS cov;
DROP TABLE IF EXISTS customer;
DROP TABLE IF EXISTS customeraccountmap;
DROP TABLE IF EXISTS customerrackhighesttemperature;
DROP TABLE IF EXISTS customreport;
DROP TABLE IF EXISTS customreporthistory;
DROP TABLE IF EXISTS customserviceconfig;
DROP TABLE IF EXISTS customserviceconfigdisableandsort;
DROP TABLE IF EXISTS customservicegroup;
DROP TABLE IF EXISTS dailyinspectcustomization;
DROP TABLE IF EXISTS dailyinspecttask;
DROP TABLE IF EXISTS dailyroominspecttask;
DROP TABLE IF EXISTS dailyroominspecttaskresult;
DROP TABLE IF EXISTS dashboard;
DROP TABLE IF EXISTS dashboardpanel;
DROP TABLE IF EXISTS databasechangelog;
DROP TABLE IF EXISTS databasechangeloglock;
DROP TABLE IF EXISTS datasource;
DROP TABLE IF EXISTS defect;
DROP TABLE IF EXISTS defectcategory;
DROP TABLE IF EXISTS defectevent;
DROP TABLE IF EXISTS defecttagmap;
DROP TABLE IF EXISTS delegateprocessinstancelog;
DROP TABLE IF EXISTS desiredcov;
DROP TABLE IF EXISTS device;
DROP TABLE IF EXISTS devicebook;
DROP TABLE IF EXISTS devicebooktagmap;
DROP TABLE IF EXISTS devicebranch;
DROP TABLE IF EXISTS devicecategory;
DROP TABLE IF EXISTS devicecheckitemresult;
DROP TABLE IF EXISTS devicechecktask;
DROP TABLE IF EXISTS devicechecktaskstate;
DROP TABLE IF EXISTS devicecorepointmap;
DROP TABLE IF EXISTS devicecoresourcemap;
DROP TABLE IF EXISTS devicecustomercategory;
DROP TABLE IF EXISTS devicediseaseexaminationitem;
DROP TABLE IF EXISTS devicediseaseindex;
DROP TABLE IF EXISTS devicediseaserule;
DROP TABLE IF EXISTS deviceinstalllog;
DROP TABLE IF EXISTS devicemaintenanceinfo;
DROP TABLE IF EXISTS devicemaintenancelog;
DROP TABLE IF EXISTS devicemask;
DROP TABLE IF EXISTS deviceordermap;
DROP TABLE IF EXISTS deviceparameterchangelog;
DROP TABLE IF EXISTS devicerefdimension;
DROP TABLE IF EXISTS devicesignalsetting;
DROP TABLE IF EXISTS devicestate;
DROP TABLE IF EXISTS devicesubhealthexaminationitem;
DROP TABLE IF EXISTS devicesubhealthindex;
DROP TABLE IF EXISTS devicesubhealthindexlog;
DROP TABLE IF EXISTS devicesubhealthrule;
DROP TABLE IF EXISTS devicetransferlog;
DROP TABLE IF EXISTS dimensionconfigure;
DROP TABLE IF EXISTS dimensiondesigner;
DROP TABLE IF EXISTS dimensionmodel;
DROP TABLE IF EXISTS diskfile;
DROP TABLE IF EXISTS dispatchticket;
DROP TABLE IF EXISTS dispatchtickettagmap;
DROP TABLE IF EXISTS document;
DROP TABLE IF EXISTS documenttagmap;
DROP TABLE IF EXISTS documenttype;
DROP TABLE IF EXISTS drawdesigns;
DROP TABLE IF EXISTS duty;
DROP TABLE IF EXISTS earlywarningcategory;
DROP TABLE IF EXISTS earlywarningcondition;
DROP TABLE IF EXISTS emailmessage;
DROP TABLE IF EXISTS energy_dataentry;
DROP TABLE IF EXISTS energy_dataitem;
DROP TABLE IF EXISTS energy_dimensiontype;
DROP TABLE IF EXISTS energy_elecfeeconfigoperatelog;
DROP TABLE IF EXISTS energy_elecfeefpg;
DROP TABLE IF EXISTS energy_elecfeefpgvalue;
DROP TABLE IF EXISTS energy_elecfeescheme;
DROP TABLE IF EXISTS energy_elecfeeschemestructuremap;
DROP TABLE IF EXISTS energy_elecfeestepprice;
DROP TABLE IF EXISTS energy_objectmap;
DROP TABLE IF EXISTS energy_structure;
DROP TABLE IF EXISTS energymanagement;
DROP TABLE IF EXISTS energymanagementmap;
DROP TABLE IF EXISTS entityinfo;
DROP TABLE IF EXISTS eventconvergencerule;
DROP TABLE IF EXISTS eventstate;
DROP TABLE IF EXISTS eventstaticbyday;
DROP TABLE IF EXISTS extfieldconfiguration;
DROP TABLE IF EXISTS extvalueconfiguration;
DROP TABLE IF EXISTS fault;
DROP TABLE IF EXISTS faultcategory;
DROP TABLE IF EXISTS feature;
DROP TABLE IF EXISTS flight;
DROP TABLE IF EXISTS followupticket;
DROP TABLE IF EXISTS followuptickettagmap;
DROP TABLE IF EXISTS gateway;
DROP TABLE IF EXISTS gender;
DROP TABLE IF EXISTS globalresource;
DROP TABLE IF EXISTS globalresourcecategory;
DROP TABLE IF EXISTS globalresourcecategorymap;
DROP TABLE IF EXISTS globalresourcetag;
DROP TABLE IF EXISTS globalresourcetagmap;
DROP TABLE IF EXISTS gocronexpression;
DROP TABLE IF EXISTS graphiccomponent;
DROP TABLE IF EXISTS graphichierarchytree;
DROP TABLE IF EXISTS graphicpage;
DROP TABLE IF EXISTS graphicpagecomponent;
DROP TABLE IF EXISTS graphicpagegroup;
DROP TABLE IF EXISTS graphicpagetemplate;
DROP TABLE IF EXISTS graphicpagetemplatecomponent;
DROP TABLE IF EXISTS graphicpagetemplategroup;
DROP TABLE IF EXISTS healthycheckresult;
DROP TABLE IF EXISTS historyactivetaskstatistics;
DROP TABLE IF EXISTS historycommand;
DROP TABLE IF EXISTS historydevicecheckitemresult;
DROP TABLE IF EXISTS historydevicechecktask;
DROP TABLE IF EXISTS historyevent;
DROP TABLE IF EXISTS historypassword;
DROP TABLE IF EXISTS historypersonalactivetaskstatistics;
DROP TABLE IF EXISTS historysmartform;
DROP TABLE IF EXISTS historysmartformitem;
DROP TABLE IF EXISTS historysmartformsubitem;
DROP TABLE IF EXISTS historytakedevicepointconfig;
DROP TABLE IF EXISTS imei;
DROP TABLE IF EXISTS itdevice;
DROP TABLE IF EXISTS itdevicemodel;
DROP TABLE IF EXISTS jpushappusermap;
//DROP TABLE IF EXISTS license;
//DROP TABLE IF EXISTS licensefeaturecontent;
DROP TABLE IF EXISTS linkconfig;
DROP TABLE IF EXISTS linkconfigtemplate;
DROP TABLE IF EXISTS linkelement;
DROP TABLE IF EXISTS linkelementconfig;
DROP TABLE IF EXISTS linkgroup;
DROP TABLE IF EXISTS linkinstance;
DROP TABLE IF EXISTS linknode;
DROP TABLE IF EXISTS linksegment;
DROP TABLE IF EXISTS liveevent;
DROP TABLE IF EXISTS liveeventfiltertemplate;
DROP TABLE IF EXISTS liveeventoperationlog;
DROP TABLE IF EXISTS livepoint;
DROP TABLE IF EXISTS location;
DROP TABLE IF EXISTS loginlog;
DROP TABLE IF EXISTS maintaindevice;
DROP TABLE IF EXISTS maintainplan;
DROP TABLE IF EXISTS maintainplandevicemap;
DROP TABLE IF EXISTS maintainplansmartformitem;
DROP TABLE IF EXISTS maintainplansmartformsubitemresult;
DROP TABLE IF EXISTS maintenanceperformance;
DROP TABLE IF EXISTS maintenancerecord;
DROP TABLE IF EXISTS materialchange;
DROP TABLE IF EXISTS materialitem;
DROP TABLE IF EXISTS materialwarehouse;
DROP TABLE IF EXISTS matrixchart;
DROP TABLE IF EXISTS menuitem;
DROP TABLE IF EXISTS menuitemstructuremap;
DROP TABLE IF EXISTS menuprofile;
DROP TABLE IF EXISTS menustructure;
DROP TABLE IF EXISTS mytasknotificationconfig;
DROP TABLE IF EXISTS navigationdock;
DROP TABLE IF EXISTS navigationdocktargetmap;
DROP TABLE IF EXISTS navigationdocktype;
DROP TABLE IF EXISTS navigationtarget;
DROP TABLE IF EXISTS networkdevice;
DROP TABLE IF EXISTS note;
DROP TABLE IF EXISTS notification;
DROP TABLE IF EXISTS notificationdeletestatus;
DROP TABLE IF EXISTS notificationreadstatus;
DROP TABLE IF EXISTS notificationreceiver;
DROP TABLE IF EXISTS objecttype;
DROP TABLE IF EXISTS objecttypesubtypefeaturemap;
DROP TABLE IF EXISTS organization;
DROP TABLE IF EXISTS organizationcalendar;
DROP TABLE IF EXISTS permission;
DROP TABLE IF EXISTS permissioncategory;
DROP TABLE IF EXISTS person;
DROP TABLE IF EXISTS personalschedule;
DROP TABLE IF EXISTS personalschedulecaptain;
DROP TABLE IF EXISTS personcallingcardmap;
DROP TABLE IF EXISTS persondevicemap;
DROP TABLE IF EXISTS persondutymap;
DROP TABLE IF EXISTS personfingerprinttemplate;
DROP TABLE IF EXISTS persongroup;
DROP TABLE IF EXISTS persongroupcategory;
DROP TABLE IF EXISTS persongroupmap;
DROP TABLE IF EXISTS phoenixassetbuilding;
DROP TABLE IF EXISTS phoenixassetdeviceofrack;
DROP TABLE IF EXISTS phoenixassetfloor;
DROP TABLE IF EXISTS phoenixassetgeneral;
DROP TABLE IF EXISTS phoenixassetmdc;
DROP TABLE IF EXISTS phoenixassetpark;
DROP TABLE IF EXISTS phoenixassetrack;
DROP TABLE IF EXISTS phoenixassetroom;
DROP TABLE IF EXISTS pointmask;
DROP TABLE IF EXISTS powerdevice;
DROP TABLE IF EXISTS powerdeviceconnection;
DROP TABLE IF EXISTS prealarm;
DROP TABLE IF EXISTS prealarmcategory;
DROP TABLE IF EXISTS prealarmhistory;
DROP TABLE IF EXISTS prealarmpoint;
DROP TABLE IF EXISTS prealarmseverity;
DROP TABLE IF EXISTS primarykeyvalue;
DROP TABLE IF EXISTS processinstancelog;
DROP TABLE IF EXISTS processinstancelogvariable;
DROP TABLE IF EXISTS protocolmapper;
DROP TABLE IF EXISTS QRTZ_FIRED_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_PAUSED_TRIGGER_GRPS;
DROP TABLE IF EXISTS QRTZ_SCHEDULER_STATE;
DROP TABLE IF EXISTS QRTZ_LOCKS;
DROP TABLE IF EXISTS QRTZ_SIMPLE_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_SIMPROP_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_CRON_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_BLOB_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_JOB_DETAILS;
DROP TABLE  IF EXISTS QRTZ_CALENDARS;

DROP TABLE IF EXISTS rack;
DROP TABLE IF EXISTS rackdevice;
DROP TABLE IF EXISTS rackdevicemodel;
DROP TABLE IF EXISTS rackdeviceonoffracksubticket;
DROP TABLE IF EXISTS rackdeviceonoffrackticket;
DROP TABLE IF EXISTS rackdeviceposition;
DROP TABLE IF EXISTS rackdeviceshiftrecord;
DROP TABLE IF EXISTS rackpowercapacity;
DROP TABLE IF EXISTS rackpoweronticket;
DROP TABLE IF EXISTS rackpoweronticketrackmap;
DROP TABLE IF EXISTS regionmask;
DROP TABLE IF EXISTS repairrecord;
DROP TABLE IF EXISTS report;
DROP TABLE IF EXISTS reportcategory;
DROP TABLE IF EXISTS reportentryconfig;
DROP TABLE IF EXISTS reportexportparameterpreset;
DROP TABLE IF EXISTS reportparameterpreset;
DROP TABLE IF EXISTS reportschema;
DROP TABLE IF EXISTS reportschemacategory;
DROP TABLE IF EXISTS reportschemaexportparameter;
DROP TABLE IF EXISTS reportschemaqueryparameter;
DROP TABLE IF EXISTS reporttimingtaskfile;
DROP TABLE IF EXISTS reporttimingtaskmanagement;
DROP TABLE IF EXISTS reporttimingtasktimetype;
/*
DROP TABLE IF EXISTS resourcestructure;
*/
DROP TABLE IF EXISTS resourcestructuretag;
DROP TABLE IF EXISTS resourcestructuretagmap;
DROP TABLE IF EXISTS resourcetagsubtypefeaturemap;
DROP TABLE IF EXISTS responsibilitydutylog;
DROP TABLE IF EXISTS responsibilitypersongroupdevicecategorymap;
DROP TABLE IF EXISTS riskticket;
DROP TABLE IF EXISTS role;
DROP TABLE IF EXISTS rolepermissionmap;
DROP TABLE IF EXISTS roleresourcestructuremap;
DROP TABLE IF EXISTS roomaccessrecord;
DROP TABLE IF EXISTS roomaccompanypersonmap;
DROP TABLE IF EXISTS roomcategory;
DROP TABLE IF EXISTS ruledetect;
DROP TABLE IF EXISTS ruledetectitem;
DROP TABLE IF EXISTS ruledetectitemparam;
DROP TABLE IF EXISTS ruleevaluation;
DROP TABLE IF EXISTS ruleevaluationitem;
DROP TABLE IF EXISTS ruleevaluationitemmap;
DROP TABLE IF EXISTS scene;
DROP TABLE IF EXISTS scenepermissionmap;
DROP TABLE IF EXISTS scenestructure;
DROP TABLE IF EXISTS schedulegroup;
DROP TABLE IF EXISTS scheduleperson;
DROP TABLE IF EXISTS schedulingflightmap;
DROP TABLE IF EXISTS securityclassification;
DROP TABLE IF EXISTS shiftchangecheckitemresult;
DROP TABLE IF EXISTS shiftchangecheckitems;
DROP TABLE IF EXISTS shiftchangerecord;
DROP TABLE IF EXISTS shiftresponsibilityalarmmap;
DROP TABLE IF EXISTS shiftresponsibilityattendancerecord;
DROP TABLE IF EXISTS shiftresponsibilityrecord;
DROP TABLE IF EXISTS site;
DROP TABLE IF EXISTS sitedevicemap;
DROP TABLE IF EXISTS sitestructure;
DROP TABLE IF EXISTS sitestructuremap;
DROP TABLE IF EXISTS smartform;
DROP TABLE IF EXISTS smartformcategory;
DROP TABLE IF EXISTS smartformitem;
DROP TABLE IF EXISTS smartformsubitem;
DROP TABLE IF EXISTS smsmessage;
DROP TABLE IF EXISTS softwareversion;
DROP TABLE IF EXISTS startaskmap;
DROP TABLE IF EXISTS staterule;
DROP TABLE IF EXISTS subscene;
DROP TABLE IF EXISTS subscenecompmap;
DROP TABLE IF EXISTS subscenemenuprofilemap;
DROP TABLE IF EXISTS subscenereportmap;
DROP TABLE IF EXISTS systemconfig;
DROP TABLE IF EXISTS systemlicensekey;
DROP TABLE IF EXISTS systemnavigation;
DROP TABLE IF EXISTS tag;
DROP TABLE IF EXISTS tagtype;
DROP TABLE IF EXISTS task;
DROP TABLE IF EXISTS taskcomment;
DROP TABLE IF EXISTS taskfile;
DROP TABLE IF EXISTS taskgroup;
DROP TABLE IF EXISTS tasknotificationconfig;
DROP TABLE IF EXISTS taskparticipantmap;
DROP TABLE IF EXISTS tasktag;
DROP TABLE IF EXISTS tasktagmap;
/*
DROP TABLE IF EXISTS tbl_commandbasedic;
DROP TABLE IF EXISTS tbl_control;
DROP TABLE IF EXISTS tbl_controlmeanings;
DROP TABLE IF EXISTS tbl_dataentry;
DROP TABLE IF EXISTS tbl_dataitem;
DROP TABLE IF EXISTS tbl_drivestructuretemplate;
DROP TABLE IF EXISTS tbl_drivetemplate;
DROP TABLE IF EXISTS tbl_equipment;
DROP TABLE IF EXISTS tbl_equipmentbasetype;
DROP TABLE IF EXISTS tbl_equipmentext;
DROP TABLE IF EXISTS tbl_equipmenttemplate;
DROP TABLE IF EXISTS tbl_event;
DROP TABLE IF EXISTS tbl_eventbasedic;
DROP TABLE IF EXISTS tbl_eventcondition;

DROP TABLE IF EXISTS tbl_signal;
DROP TABLE IF EXISTS tbl_signalbasedic;
DROP TABLE IF EXISTS tbl_signalmeanings;
DROP TABLE IF EXISTS tbl_signalproperty;
DROP TABLE IF EXISTS tbl_station;
DROP TABLE IF EXISTS tbl_statusbasedic;
DROP TABLE IF EXISTS tsl_monitorunit;
DROP TABLE IF EXISTS tsl_monitorunitconfig;
DROP TABLE IF EXISTS tsl_port;
DROP TABLE IF EXISTS tsl_sampler;
DROP TABLE IF EXISTS tsl_samplerunit;
*/
DROP TABLE IF EXISTS tbl_logiccategorybasedic;
DROP TABLE IF EXISTS tbl_rackmountrecord;
DROP TABLE IF EXISTS udevice;
DROP TABLE IF EXISTS udevicecoresourcemap;
DROP TABLE IF EXISTS warehouse;
DROP TABLE IF EXISTS weatherrecord;
DROP TABLE IF EXISTS widgetdatasource;
DROP TABLE IF EXISTS widgettemplate;
DROP TABLE IF EXISTS widgettemplatetagmap;
DROP TABLE IF EXISTS workbenchserviceconfig;
DROP TABLE IF EXISTS workbenchservicelayoutparams;
DROP TABLE IF EXISTS workshiftpersonresponsibilitymap;

alter table resourcestructure change ObjectTypeId StructureTypeId int; 

alter table tbl_account add Avatar varchar(255);
alter table tbl_account add ThemeName varchar(128);
alter table tbl_account add NeedResetPwd tinyint(1) default 0;

alter table tbl_Equipment add resourcestructureId int not null default 0;
alter table tbl_activeevent add EndValue double;
alter table tbl_activeevent add ConvergenceEventId bigint  not null default 0;
alter table tbl_activeevent add BaseEquipmentId int  not null default 0;
alter table tbl_activeevent add EventLevel int  not null default 0;
alter table tbl_activeevent add ResourceStructureId int not null default 0;

alter table tbl_alarmchange add EndValue double;
alter table tbl_alarmchange add ConvergenceEventId bigint  not null default 0;
alter table tbl_alarmchange add BaseEquipmentId int  not null default 0;
alter table tbl_alarmchange add EventLevel int  not null default 0;
alter table tbl_alarmchange add ResourceStructureId int not null default 0;

alter table tbl_historyevent add EndValue double;
alter table tbl_historyevent add ConvergenceEventId bigint  not null default 0;
alter table tbl_historyevent add BaseEquipmentId int  not null default 0;
alter table tbl_historyevent add EventLevel int  not null default 0;
alter table tbl_historyevent add ResourceStructureId int not null default 0;

alter table tbl_equipmenttemplate add photo varchar(128);

insert into tbl_configchangedefine values (27, 'ResourceStructureOP', 'ResourceStructure','ResourceStructureId');
insert into tbl_configchangedefine values (28, 'ResourceStructureOP', 'tbl_equipment','ResourceStructureId.EquipmentId');


insert into TBL_ConfigChangeMap values(27,1,27,1,1);
insert into TBL_ConfigChangeMap values(27,2,27,2,1);
insert into TBL_ConfigChangeMap values(27,3,27,3,1);

insert into TBL_ConfigChangeMap values(28,1,28,1,1);
insert into TBL_ConfigChangeMap values(28,2,28,2,1);
insert into TBL_ConfigChangeMap values(28,3,28,3,1);

CALL PIL_InitDictionaryEntryItem( 23,4,0,0,1,'五级告警','','Level 5');
CALL PIL_InitDictionaryEntryItem( 23,5,0,0,1,'六级告警','','Level 6');
CALL PIL_InitDictionaryEntryItem( 23,6,0,0,1,'七级告警','','Level 7');
CALL PIL_InitDictionaryEntryItem( 23,7,0,0,1,'八级告警','','Level 8');

update tbl_dataItem set extendfield3='#77BDF8',extendfield4=4 where entryId=23 and ItemId = 0;
update tbl_dataItem set extendfield3='#EDD951',extendfield4=3 where entryId=23 and ItemId = 1;
update tbl_dataItem set extendfield3='#F39924',extendfield4=2 where entryId=23 and ItemId = 2;
update tbl_dataItem set extendfield3='#ff2626',extendfield4=1 where entryId=23 and ItemId = 3;
update tbl_dataItem set extendfield3='#FF9255',extendfield4=5 where entryId=23 and ItemId = 4;
update tbl_dataItem set extendfield3='#007FFF',extendfield4=6 where entryId=23 and ItemId = 5;
update tbl_dataItem set extendfield3='#8694FB',extendfield4=7 where entryId=23 and ItemId = 6;
update tbl_dataItem set extendfield3='#08AE92',extendfield4=8 where entryId=23 and ItemId = 7;


update TBL_Equipment a inner join tbl_roomdevicemap b  on a.equipmentId = b.deviceId
inner join resourcestructure c on b.roomId = c.OriginId and c.StructureTypeId = 4
set a.ResourceStructureId = c.ResourceStructureId;

update tbl_activeevent set EventLevel = 4-EventSeverityId

update TBL_ActiveEvent a inner join tbl_Equipment b  on a.StationId = b.stationId and a.equipmentId = b.equipmentId
set a.ResourceStructureId = b.ResourceStructureId;