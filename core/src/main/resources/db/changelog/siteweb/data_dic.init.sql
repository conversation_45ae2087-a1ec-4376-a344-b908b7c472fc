# 字典项初始化
# 审计报表等级
INSERT INTO tbl_dataentry VALUES(3001,1,'审计级别',NULL,'Audit level',1,'');
CALL PIL_InitDictionaryEntryItem( 3001,1,0,0,1,'最小级别','','min level');
CALL PIL_InitDictionaryEntryItem( 3001,2,0,0,1,'基本级别','','basic level');
CALL PIL_InitDictionaryEntryItem( 3001,3,0,0,1,'详细级别','','detail level');
CALL PIL_InitDictionaryEntryItem( 3001,4,0,0,1,'未定义级别','','undefined level');
# 安全日志报表类别
INSERT INTO tbl_dataentry VALUES(3002,1,'类别',NULL,'Type',1,'');
CALL PIL_InitDictionaryEntryItem( 3002,1,0,0,1,'身份用户鉴别','','identity authentication');
CALL PIL_InitDictionaryEntryItem( 3002,2,0,0,1,'攻击检测','','attack detection');
CALL PIL_InitDictionaryEntryItem( 3002,3,0,0,1,'暴力破解','','brute force');
CALL PIL_InitDictionaryEntryItem( 3002,4,0,0,1,'完整性检测','','integrity test');
# IT设备模型类型
INSERT INTO tbl_dataentry VALUES(3003,1,'IT设备模型类型',NULL,'Category',1,'');
# 卡号类型
INSERT INTO tbl_dataentry VALUES(3004,1,'卡号类型',NULL,'Card number type',1,'');
CALL PIL_InitDictionaryEntryItem( 3004,10,0,0,1,'十进制','','decimal');
CALL PIL_InitDictionaryEntryItem( 3004,16,0,0,1,'十六进制','','hexadecimal');
# 组态模板组件类型
INSERT INTO tbl_dataentry VALUES(3005,1,'组态模板组件类型',NULL,'Configure the template graphic type',1,'');
CALL PIL_InitDictionaryEntryItem( 3005,1,0,0,1,'全部','','all');
CALL PIL_InitDictionaryEntryItem( 3005,2,0,0,1,'动环','','power & environment supervision');
CALL PIL_InitDictionaryEntryItem( 3005,3,0,0,1,'通用','','general');
CALL PIL_InitDictionaryEntryItem( 3005,4,0,0,1,'指标类','','complex index');
CALL PIL_InitDictionaryEntryItem( 3005,5,0,0,1,'能耗','','energy');
CALL PIL_InitDictionaryEntryItem( 3005,6,0,0,1,'定制','','customize');
CALL PIL_InitDictionaryEntryItem( 3005,7,0,0,1,'图表','','chart');