<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_MenuProfile_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MenuProfile"
                     remarks="MenuProfile info table">
            <column name="MenuProfileId" type="int" remarks="菜单方案ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Name" type="varchar(255)" remarks="方案名称">
                <constraints nullable="true"/>
            </column>
            <column name="Checked" type="tinyint(1)" remarks="是否被选中">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="MenuProfile" columnName="MenuProfileId" incrementBy="1" columnDataType="int" startWith="1000"/>
    </changeSet>
</databaseChangeLog>