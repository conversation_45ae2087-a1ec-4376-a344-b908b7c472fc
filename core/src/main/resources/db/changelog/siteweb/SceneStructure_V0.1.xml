<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_sceneStructure_info" author="wxc">
        <createTable tableName="SceneStructure" remarks="sceneStructure info table">
            <column name="SceneStructureId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="DisplayIndex" type="int">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SceneStructure" columnName="SceneStructureId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>