<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_assetCategory_info" author="wxc">
        <createTable tableName="AssetCategory" remarks="资产类型表">
            <column name="AssetCategoryId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AssetCategoryName" type="varchar(128)" remarks="资产类型名称">
                <constraints nullable="true"/>
            </column>
            <column name="Remarks" type="varchar(255)" remarks="备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AssetCategory" columnName="AssetCategoryId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>