<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="DiskFile" author="wiliam"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DiskFile"
                     remarks="DiskFile info table">
            <column name="FileId" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="FilePath" type="varchar(128)">
                <constraints nullable="false" />
            </column>
            <column name="FileName" type="varchar(128)">
                <constraints nullable="false" />
            </column>
            <column name="Status" type="int">
                <constraints nullable="true" />
            </column>
            <column name="CreateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="DiskFile" columnName="FileId" incrementBy="1"  columnDataType ="bigint" startWith="1" />
    </changeSet>
</databaseChangeLog>