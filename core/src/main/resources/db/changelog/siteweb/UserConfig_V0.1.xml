<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_userconfig_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="UserConfig"
                     remarks="basic UserConfig info table">
            <column name="UserConfigId" type="int" remarks="用户配置主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false"/>
            </column>
            <column name="ConfigType" type="int" remarks="配置类型 1TTS">
                <constraints nullable="false"/>
            </column>
            <column name="ConfigKey" type="varchar(255)" remarks="用户信息配置键">
                <constraints nullable="false"/>
            </column>
            <column name="ConfigValue" type="varchar(255)" remarks="用户信息配置值">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="UserConfig" columnName="UserConfigId" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="UserConfig" indexName="IDX_UserId_UserType">
            <column name="UserId"/>
            <column name="ConfigType"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>