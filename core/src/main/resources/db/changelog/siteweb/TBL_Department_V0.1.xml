<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_department_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Department" remarks="department info table">
            <column name="DepartmentId" type="int" remarks="部门Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DepartmentName" type="varchar(128)" remarks="部门名称">
                <constraints nullable="false"/>
            </column>
            <column name="DepartmentLevel" type="varchar(20)" remarks="部门等级">
                <constraints nullable="true"/>
            </column>
            <column name="DepartmentFunction" type="varchar(40)" remarks="部门职能">
                <constraints nullable="true"/>
            </column>
            <column name="ParentDeprtId" type="int" remarks="父部门Id">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="LastUpdateDate" type="datetime" remarks="修改时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>