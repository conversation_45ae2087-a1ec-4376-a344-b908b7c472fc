<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="SecurityReport" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="SecurityReport" remarks="安全日志报表">
            <column name="SecurityReportId" type="int" remarks="安全日志报表主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="OperationAccount" type="varchar(128)" remarks="操作账户">
                <constraints nullable="true" />
            </column>
            <column name="Type" type="int" remarks="类别">
                <constraints nullable="true" />
            </column>
            <column name="ClientIP" type="varchar(50)" remarks="客户端ip">
                <constraints nullable="true" />
            </column>
            <column name="Details" type="text" remarks="详情">
                <constraints nullable="true" />
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SecurityReport" columnName="SecurityReportId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>