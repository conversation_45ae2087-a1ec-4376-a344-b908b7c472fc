<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_PreAlarmChange_info" author="lz" >
        <createTable tableName="prealarmchange" remarks="PreAlarm state change table">
            <column name="sequenceId" type="varchar(128)">
                <constraints nullable="false" />
            </column>
            <column name="serialNo" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true" />
            </column>
            <column name="operationType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="preAlarmId" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="preAlarmName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="meanings" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="triggerValue" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="preAlarmPointId" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="preAlarmCategory" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="preAlarmCategoryName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="preAlarmSeverity" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="preAlarmSeverityName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="startTime" type="dateTime">
                <constraints nullable="false"/>
            </column>
            <column name="endTime" type="dateTime">
                <constraints nullable="true"/>
            </column>
            <column name="confirmTime" type="dateTime">
                <constraints nullable="true"/>
            </column>
            <column name="confirmorId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="confirmorName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="equipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="equipmentName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="equipmentCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="equipmentCategoryName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="equipmentVendor" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="centerId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="centerName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="structureId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="structureName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="stationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StationName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="stationCategoryId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="resourceStructureId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="levelOfPathName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="insertTime" type="dateTime">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="prealarmchange" columnName="serialNo" incrementBy="1" columnDataType="bigint" startWith="1"/>
    </changeSet>
</databaseChangeLog>