<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblDataItem_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="tbl_dataitem" remarks="tbl_dataitem">
            <column name="EntryItemId" type="INT" remarks="唯一ID">
                <constraints nullable="false" primaryKey="true" unique="true"/>
            </column>
            <column defaultValueNumeric="0" name="ParentEntryId" type="INT" remarks="父字典ID">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="ParentItemId" type="INT" remarks="父字典项ID">
                <constraints nullable="false"/>
            </column>
            <column name="EntryId" type="INT" remarks="字典ID">
                <constraints nullable="false"/>
            </column>
            <column name="ItemId" type="INT" remarks="字典项ID">
                <constraints nullable="false"/>
            </column>
            <column name="ItemValue" type="VARCHAR(128)" remarks="字典项值">
                <constraints nullable="false"/>
            </column>
            <column name="ItemAlias" type="VARCHAR(255)" remarks="字典项英文值"/>
            <column defaultValue="1" name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="IsSystem" type="tinyint(1)" remarks="是否为系统">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="0" name="IsDefault" type="tinyint(1)" remarks="是否为默认">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息"/>
            <column name="ExtendField1" type="VARCHAR(255)" remarks="扩展字段1"/>
            <column name="ExtendField2" type="VARCHAR(255)" remarks="扩展字段2"/>
            <column name="ExtendField3" type="VARCHAR(255)" remarks="扩展字段3"/>
            <column name="ExtendField4" type="VARCHAR(255)" remarks="扩展字段4"/>
            <column name="ExtendField5" type="VARCHAR(255)" remarks="扩展字段5"/>
        </createTable>
    </changeSet>
</databaseChangeLog>