<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

<changeSet id="create_tblControl_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Control" remarks="tblControl info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="int" remarks="控制ID">
                <constraints nullable="false"/>
            </column>
            <column name="ControlName" type="varchar(128)" remarks="控制名">
                <constraints nullable="false"/>
            </column>
            <column name="ControlCategory" type="int" remarks="控制类型">
                <constraints nullable="false"/>
            </column>
            <column name="CmdToken" type="varchar(255)" remarks="控制命令串">
                <constraints nullable="false"/>
            </column>
            <column name="BaseTypeId" type="bigint" remarks="控制基类ID">
                <constraints nullable="true"/>
            </column>
            <column name="ControlSeverity" type="int" remarks="控制等级">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int" remarks="对应信号ID">
                <constraints nullable="true"/>
            </column>
            <column name="TimeOut" type="int" remarks="超时时间">
                <constraints nullable="true"/>
            </column>
            <column name="Retry" type="int" remarks="重试次数">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="Visible" type="tinyint(1)" remarks="是否可见">
                <constraints nullable="false"/>
            </column>
            <column name="DisplayIndex" type="int" remarks="显示顺序">
                <constraints nullable="false"/>
            </column>
            <column name="CommandType" type="int" remarks="命令类型">
                <constraints nullable="false"/>
            </column>
            <column name="ControlType" type="smallint" remarks="控制类型">
                <constraints nullable="true"/>
            </column>
            <column name="DataType" type="smallint" remarks="数据类型">
                <constraints nullable="true"/>
            </column>
            <column name="MaxValue" type="double" remarks="最大值">
                <constraints nullable="false"/>
            </column>
            <column name="MinValue" type="double" remarks="最小值">
                <constraints nullable="false"/>
            </column>
            <column name="DefaultValue" type="double" remarks="默认值">
                <constraints nullable="true"/>
            </column>
            <column name="ModuleNo" type="int" remarks="模块号">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="TBL_Control" indexName="idx_Control_Id" unique="true">
            <column name="EquipmentTemplateId"></column>
            <column name="ControlId"></column>
        </createIndex>
        <addAutoIncrement tableName="TBL_Control" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>