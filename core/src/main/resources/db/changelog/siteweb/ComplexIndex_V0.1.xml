<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_complexIndex_info" author="wxc">
        <createTable tableName="ComplexIndex"
                     remarks="basic complexIndex info table">
            <column name="ComplexIndexId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ComplexIndexName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ComplexIndexDefinitionId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="CalcCron" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="CalcType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="AfterCalc" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="SaveCron" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Accuracy" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Label" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BusinessTypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CheckExpression" type="varchar(1024)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComplexIndex" columnName="ComplexIndexId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>