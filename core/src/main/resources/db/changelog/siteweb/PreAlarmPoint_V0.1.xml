<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_PreAlarmPoint_info" author="xueyi" runOnChange="true">
        <createTable tableName="PreAlarmPoint" remarks="PreAlarmPoint info table">
            <column name="PreAlarmPointId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PreAlarmPointName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Meanings" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="AbnormalExpression" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="ExecuteCron" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UniqueId" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="LevelOfPath" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="LevelOfPathName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmSeverity" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Enable" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(10)">
                <constraints nullable="true"/>
            </column>
            <column name="MaskType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="MaskDuration" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="MaskStartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="MaskEndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Stateful" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Modifier" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ModifierName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ModifyTime" type="datetime">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="PreAlarmPoint" columnName="PreAlarmPointId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>


</databaseChangeLog>