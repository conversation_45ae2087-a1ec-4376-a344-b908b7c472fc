<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_PreAlarm_info" author="xueyi" runOnChange="true">
        <createTable tableName="PreAlarm" remarks="PreAlarm info table">
            <column name="PreAlarmId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PreAlarmPointId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="Meanings" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmSeverity" type="int">
                <constraints nullable="true" />
            </column>
            <column name="PreAlarmSeverityName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmCategory" type="int">
                <constraints nullable="true" />
            </column>
            <column name="PreAlarmCategoryName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Color" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="UniqueId" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="UniqueName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="LevelOfPath" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="LevelOfPathName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="TriggerValue" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(10)">
                <constraints nullable="true"/>
            </column>
            <column name="SampleTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="BusinessTypeId" type="int">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="PreAlarm" columnName="PreAlarmId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>