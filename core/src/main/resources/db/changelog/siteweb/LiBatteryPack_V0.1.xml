<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_li_battery_cell_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="LiBatteryPack"
                     remarks="li battery pack table">
            <column name="BatteryPackId" type="int" remarks="电池单体Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BatteryPackNumber" type="int" remarks="电池单体编号">
                <constraints nullable="false"/>
            </column>
            <column name="BatteryNumber" type="int" remarks="所属电池编号">
                <constraints nullable="true"/>
            </column>
            <column name="BatteryGroupNumber" type="int" remarks="所属电池组编号">
                <constraints nullable="true"/>
            </column>
            <column name="BatteryModuleNumber" type="int" remarks="所属电池模块编号">
                <constraints nullable="true"/>
            </column>
            <column name="BatteryPackName" type="varchar(128)" remarks="电池单体名称">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int" remarks="电池组设备Id">
                <constraints nullable="true"/>
            </column>
            <column name="UPSEquipmentId" type="int" remarks="UPS设备Id">
                <constraints nullable="true"/>
            </column>
            <column name="MeanVoltageComplexIndexId" type="int" remarks="电池组平均电压指标Id">
                <constraints nullable="true"/>
            </column>
            <column name="MeanTemperatureComplexIndexId" type="int" remarks="电池组平均温度指标Id">
                <constraints nullable="true"/>
            </column>
            <column name="VoltageSignalId" type="int" remarks="单体电压信号Id">
                <constraints nullable="true"/>
            </column>
            <column name="TemperatureSignalId" type="int" remarks="单体温度信号Id">
                <constraints nullable="true"/>
            </column>
            <column name="TemperatureIncrementRateComplexIndexId" type="int" remarks="单体电池温升速率指标Id">
                <constraints nullable="true"/>
            </column>
            <column name="TemperatureDeviationComplexIndexId" type="int" remarks="单体电池与同组电池平均温度差值指标Id">
                <constraints nullable="true"/>
            </column>
            <column name="VoltageDeviationComplexIndexId" type="int" remarks="单体电池与同组电池平均电压差值指标Id">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="LiBatteryPack" columnName="BatteryPackId" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>