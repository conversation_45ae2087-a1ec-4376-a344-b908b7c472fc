<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblEquipment_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_Equipment" remarks="tblEquipment info table">
            <column name="StationId" type="int" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int" remarks="设备ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentName" type="varchar(128)" remarks="设备名">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentNo" type="varchar(128)" remarks="设备编码">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentModule" type="varchar(128)" remarks="设备模块">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentStyle" type="varchar(128)" remarks="设备型号">
                <constraints nullable="true"/>
            </column>
            <column name="AssetState" type="int" remarks="资产状态">
                <constraints nullable="true"/>
            </column>
            <column name="Price" type="double" remarks="资产价格">
                <constraints nullable="true"/>
            </column>
            <column name="UsedLimit" type="double" remarks="资产寿命">
                <constraints nullable="true"/>
            </column>
            <column name="UsedDate" type="datetime" remarks="启用时间">
                <constraints nullable="true"/>
            </column>
            <column name="BuyDate" type="datetime" remarks="购买日期">
                <constraints nullable="true"/>
            </column>
            <column name="Vendor" type="varchar(255)" remarks="设备厂商">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(255)" remarks="设备单位">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentCategory" type="int" remarks="设备类型（非标准）">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentType" type="int" remarks="设备分类（自诊断，虚拟设备等）">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentClass" type="int" remarks="设备大类">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentState" type="int" remarks="设备状态(用于电池)">
                <constraints nullable="false"/>
            </column>
            <column name="EventExpression" type="varchar(255)" remarks="告警抑制表达式">
                <constraints nullable="true"/>
            </column>
            <column name="StartDelay" type="double" remarks="告警开始延时">
                <constraints nullable="true"/>
            </column>
            <column name="EndDelay" type="double" remarks="告警结束延时">
                <constraints nullable="true"/>
            </column>
            <column name="Property" type="varchar(255)" remarks="设备属性">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentBaseType"  defaultValue="0" type="int" remarks="基类设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="HouseId" type="int" remarks="局房ID">
                <constraints nullable="true"/>
            </column>
            <column name="MonitorUnitId" type="int" remarks="监控单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="WorkstationId" type="int" remarks="监控主机ID">
                <constraints nullable="true"/>
            </column>
            <column name="SamplerUnitId" type="int" remarks="采集单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="DisplayIndex" type="int" remarks="显示顺序">
                <constraints nullable="false"/>
            </column>
            <column name="ConnectState" type="int" remarks="设备连接状态">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="false"/>
            </column>
            <column name="ParentEquipmentId" type="varchar(255)" remarks="父设备ID">
                <constraints nullable="true"/>
            </column>
            <column name="RatedCapacity" type="varchar(255)" remarks="额定容量">
                <constraints nullable="true"/>
            </column>
            <column name="InstalledModule" type="varchar(255)" remarks="装机模块">
                <constraints nullable="false"/>
            </column>
            <column name="ProjectName" type="varchar(255)" remarks="工程名">
                <constraints nullable="true"/>
            </column>
            <column name="ContractNo" type="varchar(255)" remarks="合同号">
                <constraints nullable="true"/>
            </column>
            <column name="InstallTime" type="datetime" remarks="安装时间">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentSN" type="varchar(255)" remarks="设备条码">
                <constraints nullable="true"/>
            </column>
            <column name="SO" type="varchar(255)" remarks="采集协议库">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureId" type="int" remarks="层级Id">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="TBL_Equipment" indexName="IDX_Equipment_Station_Id">
            <column name="StationId"></column>
        </createIndex>
        <createIndex tableName="TBL_Equipment" indexName="IDX_Equipment_BaseTypeID">
            <column name="EquipmentBaseType"></column>
        </createIndex>
        <createIndex tableName="TBL_Equipment" indexName="IDX_Equipment_Structure_Id">
            <column name="ResourceStructureId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>